# Git-Info Folder - Detailed Workflow Analysis

## 📁 Directory Structure

```
git-info/
├── README.md                    # Documentation
├── runner.sh                    # Main entry point for git-info
├── git/                         # Repository configurations
│   ├── repos.json              # Repository name mapping
│   └── usernames.json          # Username mapping
└── scripts/                     # Core scripts
    ├── functions.sh            # Main repository sync functions
    ├── git-info.sh            # Configuration utilities
    └── variables.sh           # Environment variables

```

## 🚀 Entry Points

### 1. **git-info/runner.sh** - Main Entry Point
```bash
# Usage:
./runner.sh {github|gitlab} {repo-name} {branch} [-f|--force]

# Examples:
./runner.sh github docslant main
./runner.sh gitlab blog-admin develop -f
```

### 2. **docslant/runner.sh** - Entry Point From Docslant
```bash
# Called from docslant via:
source ./git-info/scripts/git-info.sh
```

## 🔄 Main Workflow

### **Phase 1: Initialization**

1. **Load Environment Variables**
   ```bash
   # git-info/runner.sh
   source .env                    # Load environment variables
   source ./scripts/variables.sh  # Load config variables
   source ./scripts/git-info.sh   # Load utility functions
   source ./scripts/functions.sh  # Load core sync functions
   ```

2. **Parse Command Arguments**
   ```bash
   PLATFORM=$1     # github or gitlab
   REPO_NAME=$2    # Repository name
   BRANCH_NAME=$3  # Branch name (default: main)
   IS_FORCE=$4     # -f or --force (optional)
   ```

### **Phase 2: Repository Configuration Resolution**

1. **Resolve Repository URLs**
   ```bash
   # scripts/functions.sh -> sync_repo()

   # Get GitLab repo path from config
   REPO_GITLAB_FROM_CONFIG=$(get_gitlab_repo_path "$REPO_NAME" "$REPO_JSON_FILE")
   # repos.json: "blog-admin": "cslant/inhouse/blog/blog-admin"

   # Get GitHub repo key from config
   REPO_GITHUB_FROM_CONFIG=$(get_github_repo_key "$REPO_NAME" "$REPO_JSON_FILE")

   # Build URLs
   GITHUB_URL="$GITHUB_SSH_URL/$REPO_GITHUB_FROM_CONFIG.git"
   GITLAB_URL="$GITLAB_SSH_URL/$REPO_GITLAB_FROM_CONFIG.git"
   ```

2. **Determine Local Repository Path**
   ```bash
   REPO_PATH="$CSLANT_PATH/repo/$REPO_NAME"
   # Example: /Users/<USER>/Data/CSlant/repo/blog-admin
   ```

### **Phase 3: Repository Setup**

1. **Create Repository If Not Exists**
   ```bash
   # scripts/functions.sh -> create_repo()

   if [ ! -d "$REPO_PATH" ]; then
     git clone "$GITHUB_URL" "$REPO_PATH"
     cd "$REPO_PATH"
     git remote add gitlab "$GITLAB_URL"
   fi

   # Ensure remotes exist
   git remote add origin "$GITHUB_URL"   # GitHub
   git remote add gitlab "$GITLAB_URL"   # GitLab
   ```

### **Phase 4: Sync Strategy Selection**

1. **Determine Primary/Secondary Remotes**
   ```bash
   REMOTE_PRIMARY="origin"      # Default: GitHub
   REMOTE_SECONDARY="gitlab"    # Default: GitLab

   # If platform is gitlab, reverse the order
   [ "$PLATFORM" = "gitlab" ] && {
     REMOTE_PRIMARY="gitlab"
     REMOTE_SECONDARY="origin"
   }
   ```

2. **Branch vs Tag Detection**
   ```bash
   if [[ "$BRANCH_NAME" == refs/tags/* ]]; then
     # Sync tag
     TAG_NAME="${BRANCH_NAME#refs/tags/}"
     git fetch "$REMOTE_PRIMARY" tag "$TAG_NAME"
     git checkout "tags/$TAG_NAME"
     git push --force "$REMOTE_SECONDARY" "refs/tags/$TAG_NAME:refs/tags/$TAG_NAME"
   else
     # Sync branch (see Phase 5)
   fi
   ```

### **Phase 5: Branch Sync Strategies**

#### **Strategy A: Force Sync (FORCE=1)**
```bash
if [ "$FORCE" = 1 ]; then
  echo "🚨 Force syncing branch '$BRANCH_NAME' from $REMOTE_PRIMARY..."
  
  git fetch --prune "$REMOTE_PRIMARY"
  
  if git show-ref --quiet refs/heads/$BRANCH_NAME; then
    git switch "$BRANCH_NAME"
    git reset --hard "$REMOTE_PRIMARY/$BRANCH_NAME"
  else
    git switch -c "$BRANCH_NAME" "$REMOTE_PRIMARY/$BRANCH_NAME"
  fi
  
  git clean -fd
  git push --force "$REMOTE_SECONDARY" "$BRANCH_NAME"
fi
```

#### **Strategy B: Soft Sync with Conflict Detection (FORCE=0)**
```bash
else
  echo "📦 Syncing (soft) branch '$BRANCH_NAME' from $REMOTE_PRIMARY..."

  if git show-ref --quiet refs/heads/$BRANCH_NAME; then
    git switch "$BRANCH_NAME"
    git fetch "$REMOTE_PRIMARY" "$BRANCH_NAME"

    # ✨ CONFLICT DETECTION LOGIC ✨
    LOCAL_COMMIT=$(git rev-parse HEAD)
    REMOTE_COMMIT=$(git rev-parse "$REMOTE_PRIMARY/$BRANCH_NAME")
    MERGE_BASE=$(git merge-base HEAD "$REMOTE_PRIMARY/$BRANCH_NAME")

    # Check for divergence
    if [ "$LOCAL_COMMIT" != "$REMOTE_COMMIT" ] && [ "$LOCAL_COMMIT" != "$MERGE_BASE" ]; then
      # CREATE BACKUP BRANCH
      BACKUP_BRANCH="${BRANCH_NAME}-backup-$(date +%Y%m%d-%H%M%S)"
      git branch "$BACKUP_BRANCH" "$LOCAL_COMMIT"

      # PUSH BACKUP TO BOTH REMOTES
      git push "$REMOTE_SECONDARY" "$BACKUP_BRANCH"
      git push "$REMOTE_PRIMARY" "$BACKUP_BRANCH" 2>/dev/null || true
    fi

    # SYNC WITH REMOTE
    git reset --hard "$REMOTE_PRIMARY/$BRANCH_NAME"
  else
    git switch -c "$BRANCH_NAME" "$REMOTE_PRIMARY/$BRANCH_NAME"
  fi

  git push "$REMOTE_SECONDARY" "$BRANCH_NAME"
fi
```

## 🔧 Utility Functions

### **1. Repository Configuration Functions**
```bash
# scripts/git-info.sh

# Get GitLab repo path from repos.json
get_gitlab_repo_path() {
  # Input: "blog-admin", "repos.json"
  # Output: "cslant/inhouse/blog/blog-admin"
}

# Get GitHub repo key from repos.json
get_github_repo_key() {
  # Input: "cslant/inhouse/blog/blog-admin", "repos.json"
  # Output: "blog-admin"
}

# Map username according to usernames.json
get_mapped_username() {
  # Input: "tanhongit"
  # Output: "tannp"
}
```

### **2. Repository Listing**
```bash
list_repos() {
  # Display all repositories in repos.json
  cat "$CONFIG_DIR/repos.json" | grep -o "\"[^\"]*\": *\"[^\"]*\"" | cut -d '"' -f 2
}
```

## 📋 Configuration Files

### **1. git/repos.json** - Repository Mapping
```json
{
  "blog-admin": "cslant/inhouse/blog/blog-admin",
  "blog-core": "cslant/inhouse/blog/blog-core", 
  "docslant": "cslant/docslant",
  "cslant.net-flant": "cslant/cslant.net/flant"
}
```

**Purpose:**
- Map short repository names to full paths
- Support complex directory structures on GitLab/GitHub
- Easy management and path changes

### **2. git/usernames.json** - Username Mapping
```json
{
  "tanhongit": "tannp",
  "thuankg1752": "thuantt",
  "pxthinh": "thinhpx"
}
```

**Purpose:**
- Map GitHub/GitLab usernames to short names
- Standardize usernames in the system
- Support multiple accounts

## 🌐 Environment Variables

### **scripts/variables.sh**
```bash
# Repository URLs
GITHUB_SSH_URL=${GITHUB_SSH_URL:-**************:cslant}
GITLAB_SSH_URL=${GITLAB_SSH_URL:-******************:22}

# Paths
CSLANT_PATH="${ZSH_CUSTOM:-$HOME}"/Data/CSlant
CONFIG_DIR="$(pwd)/git"

# Colors for output
YELLOW='\033[1;33m'
RED='\033[1;31m'
CYAN='\033[1;36m'
GREEN='\033[1;32m'
NC='\033[0m'
```

## 🔄 Integration with Docslant

### **How git-info is used in docslant:**

1. **Import in setup/configs/import.sh**
   ```bash
   source ./git-info/scripts/git-info.sh
   ```

2. **Usage in git sync functions**
   ```bash
   # setup/git/global.sh
   git_sync_handle() {
     REPO_NAME=$1
     REPO_DIR=${2:-}

     git_force_sync "$3" "$REPO_DIR"
     git_handler "$REPO_NAME" "$REPO_DIR"
   }
   ```

3. **Configuration paths**
   ```bash
   GIT_SYNC_CONFIG_DIR="$MAIN_CURRENT_DIR/git-info"
   GIT_SYNC_CONFIG_REPO_FILE="$GIT_SYNC_CONFIG_DIR/git/repos.json"
   GIT_SYNC_CONFIG_USERNAMES_FILE="$GIT_SYNC_CONFIG_DIR/git/usernames.json"
   ```

## 🎯 Main Use Cases

### **1. Sync Repository from GitHub to GitLab**
```bash
./runner.sh github blog-admin main
```

### **2. Sync Repository from GitLab to GitHub**
```bash
./runner.sh gitlab blog-admin develop
```

### **3. Force Sync (Overwrite local changes)**
```bash
./runner.sh github docslant main -f
```

### **4. Sync Tags**
```bash
./runner.sh github blog-core refs/tags/v1.0.0
```

## ⚡ Special Features

### **1. Conflict Detection & Auto Backup**
- Automatically detect when local branch diverges from remote
- Create backup branch with timestamp
- Push backup to both remotes
- Safe for developers

### **2. Dual Remote Support**
- Support sync between GitHub and GitLab
- Automatically determine primary/secondary remote
- Bidirectional sync

### **3. Smart Repository Resolution**
- Automatically resolve repository paths from config
- Support complex repository structures
- Fallback to default naming convention

### **4. Force vs Soft Sync**
- Force: Overwrite all local changes
- Soft: Backup before sync, safer approach

## 🔍 Error Handling

### **1. Repository Not Found**
```bash
if [ ! -d "$REPO_PATH" ]; then
  git clone "$GITHUB_URL" "$REPO_PATH"
fi
```

### **2. Remote Access Issues**
```bash
git push "$REMOTE_PRIMARY" "$BACKUP_BRANCH" 2>/dev/null || echo "Could not push backup"
```

### **3. Branch Not Exists**
```bash
if git show-ref --quiet refs/heads/$BRANCH_NAME; then
  # Branch exists
else
  # Create new branch
  git switch -c "$BRANCH_NAME" "$REMOTE_PRIMARY/$BRANCH_NAME"
fi
```

## 📊 Workflow Summary

```mermaid
graph TD
    A[runner.sh] --> B[Load Config]
    B --> C[Parse Arguments]
    C --> D[Resolve Repository URLs]
    D --> E[Setup Local Repository]
    E --> F[Determine Sync Strategy]
    F --> G{Force Sync?}
    G -->|Yes| H[Force Reset & Push]
    G -->|No| I[Check Conflicts]
    I --> J{Has Conflicts?}
    J -->|Yes| K[Create Backup Branch]
    J -->|No| L[Normal Sync]
    K --> M[Push Backup]
    M --> L
    L --> N[Sync with Remote]
    N --> O[Push to Secondary]
    H --> O
    O --> P[Complete]
```

## 🎯 Conclusion

Git-info folder is a powerful repository sync system with features:

- ✅ **Dual Remote Support**: GitHub ↔ GitLab
- ✅ **Conflict Detection**: Automatic detection and backup
- ✅ **Smart Configuration**: JSON-based repository mapping
- ✅ **Force/Soft Modes**: Flexible according to needs
- ✅ **Error Handling**: Robust error recovery
- ✅ **Integration Ready**: Easy integration with docslant

This system ensures code is always safely synced between remotes without worrying about data loss.
