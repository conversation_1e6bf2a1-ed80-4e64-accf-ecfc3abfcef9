# Detailed Runner Flow Documentation

## Table of Contents
1. [System Initialization](#1-system-initialization)
2. [Command Processing](#2-command-processing)
   - [2.1. Build Commands](#21-build-commands)
     - [2.1.1. Build Command](#211-build-command)
     - [2.1.2. Build All Command](#212-build-all-command)
   - [2.2. Start Commands](#22-start-commands)
     - [2.2.1. Start Command](#221-start-command)
     - [2.2.2. Start All Command](#222-start-all-command)
   - [2.3. Git Sync Commands](#23-git-sync-commands)
     - [2.3.1. Blog Git Sync](#231-blog-git-sync)
     - [2.3.2. Home Git Sync](#232-home-git-sync)
   - [2.4. Special Install Commands](#24-special-install-commands)
   - [2.5. Client Installation Commands](#25-client-installation-commands)
   - [2.6. Resource Management](#26-resource-management)
     - [2.6.1. Resource Command](#261-resource-command)
     - [2.6.2. Resource Database](#262-resource-database)
3. [Core Module Details](#3-core-module-details)
   - [3.1. Home Module](#31-home-module)
   - [3.2. Blog Module](#32-blog-module)
   - [3.3. Documentation Module](#33-documentation-module)
4. [Utility Commands](#4-utility-commands)
   - [4.1. Welcome](#41-welcome)
   - [4.2. Help](#42-help)
   - [4.3. SSL Setup](#43-ssl-setup)
   - [4.4. Network Setup](#44-network-setup)
   - [4.5. Elasticsearch Import](#45-elasticsearch-import)
5. [All-in-One Command](#5-all-in-one-command)
6. [Important Notes](#6-important-notes)
7. [Error Handling](#7-error-handling)
8. [Complete Runner Flow Diagram](#8-complete-runner-flow-diagram)
9. [Troubleshooting](#9-troubleshooting)

## 1. System Initialization
- **File**: `runner.sh`
- **Execution Flow**:
  1. **Environment File Setup**
     - Checks for `.env` file existence
     - If missing, creates from `.env.example`
     - Uses `envsubst` if available for template processing
     - Sets up environment variables with `set -a`
     - Sources `.env` file
     - Enables strict mode with `set -ue`
  
  2. **Core Configuration**
     - Sources `variables.sh` for environment variables
     - Sources `import.sh` for function definitions
     - Calls `define_default_repos` to set up default repositories

  3. **Command Parsing**
     - Uses case statement to parse command-line arguments
     - Supports both full and short command names
     - Handles additional arguments for specific commands

## 2. Command Processing

### 2.1. Build Commands

#### 2.1.0. Build Process Flow
```mermaid
%%{init: {'theme': 'base', 'themeVariables': {
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#0d47a1',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start Build] --> B[Check Dependencies]
    B --> C[Create Network]
    C --> D[Pull Base Images]
    D --> E[Build Images]
    E --> F[Verify Build]
    F --> G[Complete]
    
    style A fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style B fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style C fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style D fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style E fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style F fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style G fill:#81c784,stroke:#2e7d32,stroke-width:2px
    
    subgraph "Build Steps"
        B --> C --> D --> E
    end
```

#### 2.1.1. Build Command
- **Command**: `./runner.sh build` or `./runner.sh b`
- **Purpose**: Builds the core Docker containers
- **Processing Flow**:
  1. Calls `build()` function in `functions.sh`
  2. `build()` calls `build_handler()` which:
     - Creates Docker network if not exists
     - Builds required Docker images
     - Pulls latest base images
- **Dependencies**:
  - Docker and Docker Compose
  - Network connectivity for image pulling
  - Sufficient disk space for images

#### 2.1.2. Build All Command
- **Command**: `./runner.sh build_all` or `./runner.sh ba`
- **Purpose**: Builds all containers including additional tools
- **Processing Flow**:
  1. Similar to build command but includes additional services
  2. Builds tools containers along with main services
- **Note**: Use this when you need all development tools

### 2.2. Start Commands

#### 2.2.0. Start Process Flow
```mermaid
%%{init: {'theme': 'mermaid', 'themeVariables': {
    'primaryColor': '#c8e6c9',
    'edgeLabelBackground':'#ffffff',
    'tertiaryColor': '#81c784',
    'lineColor': '#2e7d32',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start Services] --> B[Check Dependencies]
    B --> C[Initialize Network]
    C --> D[Start Containers]
    D --> E[Health Checks]
    E --> F[Complete]
    
    style A fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style B fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style C fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style D fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style E fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style F fill:#81c784,stroke:#2e7d32,stroke-width:2px
    
    subgraph "Start Sequence"
        B --> C --> D --> E
    end
```

#### 2.2.1. Start Command
- **Command**: `./runner.sh start` or `./runner.sh s`
- **Purpose**: Starts essential services
- **Processing Flow**:
  1. Calls `start()` function in `functions.sh`
  2. Starts core services in order:
     - Database services first
     - Backend services
     - Frontend services
     - Supporting services (mailhog, etc.)
- **Services Started**:
  - nginx
  - php84
  - mysql
  - node22
  - home-fe2
  - mailhog
  - worker84
  - elasticsearch

#### 2.2.2. Start All Command
- **Command**: `./runner.sh start_all` or `./runner.sh sa`
- **Purpose**: Starts all services including additional tools
- **Note**: Includes development and monitoring tools

### 2.3. Git Sync Commands

#### 2.3.0. Git Sync Process Flow
```mermaid
%%{init: {'theme': 'base', 'themeVariables': {
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#2e7d32',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start Git Sync] --> B[Check Access]
    B --> C[Fetch Updates]
    C --> D[Merge Changes]
    D --> E[Update Submodules]
    E --> F[Complete]
    
    style A fill:#ffecb3,stroke:#ff8f00,stroke-width:2px
    style B fill:#ffecb3,stroke:#ff8f00,stroke-width:2px
    style C fill:#ffecb3,stroke:#ff8f00,stroke-width:2px
    style D fill:#ffecb3,stroke:#ff8f00,stroke-width:2px
    style E fill:#ffecb3,stroke:#ff8f00,stroke-width:2px
    style F fill:#81c784,stroke:#2e7d32,stroke-width:2px
    
    subgraph "Sync Operations"
        B --> C --> D --> E
    end
```

#### 2.3.1. Blog Git Sync
- **Command**: `./runner.sh blog_git_sync [component] [options]` or `./runner.sh bgs`
- **Purpose**: Synchronizes blog repository components
- **Parameters**:
  - `component`: `all`, `admin`, `fe`, `api-package`
  - `options`: `-f` or `--force` to force sync
- **Processing Flow**:
  1. Validates component parameter
  2. Performs git operations based on component
  3. Handles force sync if requested
- **Notes**:
  - Requires Git to be installed
  - Needs appropriate repository access

#### 2.3.2. Home Git Sync
- **Command**: `./runner.sh home_git_sync [component] [options]` or `./runner.sh hgs`
- **Purpose**: Synchronizes home repository components
- **Parameters**:
  - `component`: `all`, `api`, `fe`
  - `options`: `-f` or `--force` to force sync

### 2.4. Special Install Commands

#### 2.4.0. Special Install Process Flow
```mermaid
%%{init: {'theme': 'base', 'themeVariables': {  
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#e64a19',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start Install] --> B{Component?}
    
    B -->|reports| C1[Setup Reports]
    B -->|docs| C2[Build Docs]
    B -->|api-docs| C3[Generate API Docs]
    B -->|home| C4[Setup Home]
    B -->|blog| C5[Setup Blog]
    
    C1 --> D1[DB Connections]
    C1 --> D2[Configure Templates]
    
    C2 --> D3[Build Documentation]
    C2 --> D4[Setup Search]
    
    C3 --> D5[Generate API Specs]
    C3 --> D6[Setup Browser]
    
    C4 --> D7[Build Frontend]
    C4 --> D8[Setup API]
    
    C5 --> D9[Run Migrations]
    C5 --> D10[Warmup Cache]
    
    D1 & D2 & D3 & D4 & D5 & D6 & D7 & D8 & D9 & D10 --> Z[Complete]
    
    style A fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style B fill:#ffccbc,stroke:#e64a19,stroke-width:2px
```

- **Command**: `./runner.sh special_install [name] [options]` or `./runner.sh si`
- **Purpose**: Handles installation of specific components with custom logic
- **Options**:
  - `reports`: Installs and configures reporting system
    - Sets up database connections
    - Configures report templates
  - `docs`: Documentation system installation
    - Builds documentation
    - Sets up search index
  - `api-docs`: API documentation
    - Generates from source code
    - Sets up API browser
  - `home`: Home page installation
    - Frontend build
    - API setup
  - `blog`: Blog system installation
    - Database migrations
    - Cache warmup
  - **... Add more as needed**

### 2.5. Client Installation Commands

#### 2.5.1. Vietnam Web Submit (vws) Flow
```mermaid
%%{init: {'theme': 'base', 'themeVariables': {
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#0d47a1',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start vws Install] --> B[Sync Repository]
    B --> C[Load Configurations]
    C --> D[Setup Database]
    D --> E[Deploy Frontend]
    E --> F[Configure API]
    F --> G[Complete]
    
    style A fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style B fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style C fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style D fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style E fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style F fill:#bbdefb,stroke:#0d47a1,stroke-width:2px
    style G fill:#81c784,stroke:#2e7d32,stroke-width:2px
```

#### 2.5.2. Comic News Flow
```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#0277bd',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start Comic News Install] --> B[Initialize Submodule]
    B --> C[Setup Content]
    C --> D[Configure Caching]
    D --> E[Apply Settings]
    E --> F[Complete]
    
    style A fill:#b3e5fc,stroke:#0277bd,stroke-width:2px
    style B fill:#b3e5fc,stroke:#0277bd,stroke-width:2px
    style C fill:#b3e5fc,stroke:#0277bd,stroke-width:2px
    style D fill:#b3e5fc,stroke:#0277bd,stroke-width:2px
    style E fill:#b3e5fc,stroke:#0277bd,stroke-width:2px
    style F fill:#81c784,stroke:#2e7d32,stroke-width:2px
```

#### 2.5.3. Common Operations
```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#f57f17',
    'textColor': '#000000'
}}}%%
flowchart LR
    A[Common Operations] --> B[Force Sync]
    A --> C[Dry Run]
    B --> D[--force]
    C --> E[--dry-run]
    D --> F[Overwrite Local]
    E --> G[Validate Access]
    
    style A fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    style B fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    style C fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    style D fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    style E fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    style F fill:#81c784,stroke:#2e7d32,stroke-width:2px
    style G fill:#81c784,stroke:#2e7d32,stroke-width:2px
```

#### 2.5.4. Access Requirements
```mermaid
%%{init: {'theme': 'base', 'themeVariables': {
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#e64a19',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Access Requirements] --> B[SSH Setup]
    A --> C[Repository Access]
    A --> D[Local Config]
    
    B --> E[SSH Key]
    B --> F[Agent Running]
    
    C --> G[Read Access]
    C --> H[Branch Permissions]
    
    D --> I[Git Config]
    D --> J[SSH Config]
    
    style A fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style B fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style C fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style D fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style E fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style F fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style G fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style H fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style I fill:#ffccbc,stroke:#e64a19,stroke-width:2px
    style J fill:#ffccbc,stroke:#e64a19,stroke-width:2px
```

### 2.5.5 Client Installation Commands

```mermaid
%%{init: {'theme': 'base', 'themeVariables': {
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#1976d2',
    'textColor': '#000000'
}}}%%
flowchart TD
    A[Start Client Install] --> B{Client Type?}
    
    B -->|vws| C1[Vietnam Web Submit]
    B -->|comic-news| C2[Comic News]
    B -->|cv... Other ...| C3[Another Platform...]
    
    %% Vietnam Web Submit Flow
    subgraph VWS[Vietnam Web Submit]
        D1[Sync Repository] --> E1[Load Configurations]
        E1 --> F1[Setup Database]
        F1 --> G1[Deploy Frontend]
        G1 --> H1[Configure API]
        style D1 fill:#d4f1f9,stroke:#333,stroke-width:2px
        style E1 fill:#d4f1f9,stroke:#333,stroke-width:2px
        style F1 fill:#d4f1f9,stroke:#333,stroke-width:2px
        style G1 fill:#d4f1f9,stroke:#333,stroke-width:2px
        style H1 fill:#d4f1f9,stroke:#333,stroke-width:2px
    end
    
    %% Comic News Flow
    subgraph CN[Comic News]
        D2[Initialize Submodule] --> E2[Setup Content]
        E2 --> F2[Configure Caching]
        F2 --> G2[Apply Settings]
        style D2 fill:#e1f5fe,stroke:#333,stroke-width:2px
        style E2 fill:#e1f5fe,stroke:#333,stroke-width:2px
        style F2 fill:#e1f5fe,stroke:#333,stroke-width:2px
        style G2 fill:#e1f5fe,stroke:#333,stroke-width:2px
    end
    
    %% Common Operations
    subgraph OPS[Common Operations]
        I[Force Sync] -->|--force| J[Overwrite Local]
        K[Dry Run] -->|--dry-run| L[Validate Access]
        style I fill:#e8f5e9,stroke:#333,stroke-width:2px
        style J fill:#e8f5e9,stroke:#333,stroke-width:2px
        style K fill:#e8f5e9,stroke:#333,stroke-width:2px
        style L fill:#e8f5e9,stroke:#333,stroke-width:2px
    end
    
    %% Access Requirements
    subgraph REQ[Access Requirements]
        M[SSH Access] --> N[Repository Permissions]
        N --> O[Local Config]
        style M fill:#fff3e0,stroke:#333,stroke-width:2px
        style N fill:#fff3e0,stroke:#333,stroke-width:2px
        style O fill:#fff3e0,stroke:#333,stroke-width:2px
    end
    
    C1 --> VWS
    C2 --> CN
    
    classDef default fill:#f5f5f5,stroke:#666,stroke-width:2px;
    classDef client1 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px;
    classDef client2 fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    classDef ops fill:#fff3e0,stroke:#ff6f00,stroke-width:2px;
    
    class VWS,CN client1;
    class OPS ops;
    class REQ client2;
```

### 2.5.6. Client Installation Process
- **Command**: `./runner.sh client_install [client] [options]` or `./runner.sh ci`
- **Purpose**: Manages installation and setup of client-specific submodules
- **Authentication**:
  - Requires SSH key with access to private repositories
  - Git credentials must be properly configured
  - Repository access permissions are verified during sync

#### Client: Vietnam Web Submit (`vws`)
- **Repository**: Private submodule in `clients/ent-vnwebsubmit-scripts`
- **Setup Flow**:
  1. **Repository Sync**:
     - Clones or updates the private repository
     - Handles force sync if requested
     - Maintains git submodule configuration
  2. **Source Loading**:
     - Sources client-specific scripts
     - Loads environment configurations
  3. **Installation**:
     - Sets up database schemas
     - Configures API endpoints
     - Deploys frontend assets

#### Client: Comic News (`comic-news`)
- **Repository**: Private submodule in dedicated client directory
- **Setup Flow**:
  1. **Repository Initialization**:
     - Handles submodule initialization
     - Manages git credentials
     - Supports force sync operations
  2. **Configuration**:
     - Sets up content sources
     - Configures caching layers
     - Applies client-specific settings

#### Common Operations
- **Force Sync**:
  - `./runner.sh client_install [client] --force`
  - Overwrites local changes with remote
  - Useful for recovery or updates

- **Dry Run**:
  - `./runner.sh client_install [client] --dry-run`
  - Validates access and configurations
  - Shows what would be installed

#### Access Requirements
1. **SSH Access**:
   - Must have SSH key added to Git provider
   - Key must be added to SSH agent
   - Access to private repositories required

2. **Repository Permissions**:
   - Read access to client repositories
   - May require specific branch access
   - Protected branch rules may apply

3. **Local Configuration**:
   - Git user.name and user.email must be set
   - SSH config should be properly configured
   - May require VPN access for private networks

### 2.6. Resource Management

#### 2.6.1. Resource Command
- **Command**: `./runner.sh resource [type]` or `./runner.sh r`
- **Purpose**: Manages application resources
- **Types**:
  - `images`: Handles image assets
  - `scripts`: Manages script files
  - `styles`: Handles CSS and styling

#### 2.6.2. Resource Database
- **Command**: `./runner.sh resource_database` or `./runner.sh rd`
- **Purpose**: Manages database resources
- **Functions**:
  - Database migrations
  - Seed data population
  - Cache clearing

## 3. Core Module Details

### 3.1. Home Module
- **Purpose**: Manages the main website interface
- **Key Functions**:
  - `home_source_implement()`: 
    - Installs frontend dependencies
    - Builds assets
    - Sets up configuration
  - `home_v2_source_implement()`: 
    - Handles version 2 specific logic
    - Manages feature flags
  - `home_api_source_implement()`: 
    - Configures API endpoints
    - Sets up authentication
    - Manages API documentation
- **Dependencies**:
  - Node.js for frontend
  - PHP for backend
  - MySQL/PostgreSQL for database

### 3.2. Blog Module
- **Purpose**: Handles blog functionality
- **Key Functions**:
  - `blog_source_implement()`: 
    - Runs database migrations
    - Sets up blog configuration
  - `blog_fe_source_implement()`: 
    - Builds frontend assets
    - Handles theme management
  - `blog_scout_import()`: 
    - Indexes content in Elasticsearch
    - Handles search functionality
- **Features**:
  - Content management
  - Categories and tags
  - Comments system
  - Search functionality

### 3.3. Documentation Module
- **Purpose**: Manages project documentation
- **Key Functions**:
  - `docs_source_implement()`: 
    - Builds documentation
    - Sets up search index
  - `api_docs_source_implement()`: 
    - Generates API documentation
    - Updates API browser
- **Features**:
  - Versioned documentation
  - Search functionality
  - Code examples
  - API reference

## 4. Utility Commands

### 4.1. Welcome
- **Command**: `./runner.sh welcome` or `./runner.sh w`
- **Purpose**: Displays welcome message and basic usage
- **Output**:
  - System information
  - Available commands
  - Quick start guide

### 4.2. Help
- **Command**: `./runner.sh help` or `./runner.sh h`
- **Purpose**: Shows detailed help information
- **Features**:
  - Command syntax
  - Available options
  - Examples
  - Troubleshooting tips

### 4.3. SSL Setup
- **Command**: `./runner.sh ssl`
- **Purpose**: Manages SSL certificates
- **Functions**:
  - Generates self-signed certificates
  - Configures web server
  - Handles certificate renewal

### 4.4. Network Setup
- **Command**: `./runner.sh network` or `./runner.sh n`
- **Purpose**: Manages Docker networks
- **Functions**:
  - Creates required networks
  - Configures network settings
  - Handles inter-container communication

### 4.5. Elasticsearch Import
- **Command**: `./runner.sh es_import` or `./runner.sh ei`
- **Purpose**: Manages Elasticsearch data
- **Functions**:
  - Imports data
  - Rebuilds indexes
  - Handles data migrations

### 4.6. Docker Sync Repo
- **Command**: `./runner.sh docker_sync_repo` or `./runner.sh dsr`
- **Purpose**: Synchronizes Docker repositories
- **Functions**:
  - Updates local Docker images
  - Syncs with remote repositories
  - Maintains version consistency

### 4.7. CSlant.net Handler
- **Command**: `./runner.sh cslant.net [subcommand]`
- **Purpose**: Manages CSlant.net specific operations
- **Subcommands**:
  - `setup`: Initial setup
  - `update`: Updates components
  - `deploy`: Deploys changes

## 5. Command Details

### 5.1. Force Sync Accept
- **Command**: Implicitly used in `all` command
- **Purpose**: Forces git sync and accepts all changes
- **Usage**: Part of the automated workflow

### 5.2. All-in-One Command
- **Command**: `./runner.sh all` or `./runner.sh a`
- **Purpose**: Complete setup with one command
- **Processing Flow**:
  1. `force_sync_accept()`: 
     - Forces git sync
     - Accepts all changes
  2. `ssl()`: 
     - Sets up SSL certificates
     - Configures web server
  3. `build()`: 
     - Builds all containers
     - Pulls latest images
  4. Git Sync: 
     - `blog_git_sync all`
     - `home_git_sync all`
  5. Resource Management:
     - `resource all`
  6. Installation:
     - `install`
  7. Service Start:
     - `start`
- **Notes**:
  - Runs all setup steps in sequence
  - Can be used for initial setup
  - Includes all dependencies

## 6. Important Notes

### 6.1. System Requirements
- **Docker**: Version 20.10.0 or higher
- **Docker Compose**: Version 1.29.0 or higher
- **Git**: Version 2.20.0 or higher
- **Disk Space**: Minimum 10GB free space
- **Memory**: Minimum 8GB RAM (16GB recommended)

### 6.2. Command Shortcuts
- `b` → `build`
- `s` → `start`
- `i` → `install`
- `u` → `update`
- `si` → `special_install`
- `ci`/`client`/`ent` → `client_install`
- `r` → `resource`
- `rd` → `resource_database`
- `a` → `all`
- `w` → `welcome`
- `h` → `help`
- `n` → `network`
- `bgs` → `blog_git_sync`
- `hgs` → `home_git_sync`
- `ei` → `es_import`
- `dsr` → `docker_sync_repo`

### 6.3. Performance Considerations
- **Parallel Processing**:
  - Build and start operations run in parallel when possible
  - Uses Docker's build cache to speed up subsequent builds
  - Network operations are optimized for concurrent execution

### 6.4. Security
- **Environment Variables**:
  - Sensitive data is stored in `.env` file
  - Never commit `.env` to version control
  - Uses secure defaults for all configurations
- **Network Security**:
  - Containers run in isolated networks
  - Only necessary ports are exposed
  - Internal communication is encrypted

## 7. Error Handling

### 7.1. Common Issues
- **Network Timeouts**:
  - Automatically retries failed operations
  - Implements exponential backoff
  - Provides clear error messages

- **Dependency Errors**:
  - Validates all required tools before execution
  - Suggests installation commands
  - Checks version compatibility

- **Permission Issues**:
  - Handles file permissions automatically
  - Provides guidance for manual fixes
  - Logs detailed error information

### 7.2. Logging
- **Log Locations**:
  - Application logs: `/var/log/app`
  - System logs: `/var/log/syslog`
  - Docker logs: `docker logs [container]`

- **Log Levels**:
  - ERROR: Critical issues requiring immediate attention
  - WARN: Potential issues that don't prevent operation
  - INFO: Standard operational messages
  - DEBUG: Detailed debugging information

### 7.3. Recovery
- **Automatic Recovery**:
  - Failed containers are automatically restarted
  - Database connection retries
  - Temporary file cleanup

- **Manual Recovery**:
  - Rollback procedures
  - Backup restoration
  - Configuration validation

## 8. Complete Runner Flow Diagram

### 8.1. System Overview
```mermaid
%%{init: {'theme': 'base', 'themeVariables': {
    'edgeLabelBackground':'#ffffff',
    'lineColor': '#558b2d',
    'textColor': '#000000'
}}}%%
flowchart TD
    %% Main Flow
    Start[Start Runner] --> Init[Initialize System]
    Init --> Parse[Parse Command]
    
    %% Command Categories
    subgraph Commands[Command Categories]
        Build[Build Commands] --> BuildFlow
        StartCmd[Start Commands] --> StartFlow
        Git[Git Sync] --> GitFlow
        Install[Install/Update] --> InstallFlow
        Client[Client Install] --> ClientFlow
        Utils[Utilities] --> UtilsFlow
    end
    
    %% Connect Main Flow
    Parse --> Commands
    
    %% Command Flows
    subgraph BuildFlow[Build Process]
        B1[Build Images] --> B2[Setup Network]
        B2 --> B3[Pull Dependencies]
    end
    
    subgraph StartFlow[Start Process]
        S1[Start Services] --> S2[Check Dependencies]
        S2 --> S3[Initialize Containers]
    end
    
    subgraph GitFlow[Git Sync Process]
        G1[Sync Repositories] --> G2[Update Submodules]
        G2 --> G3[Apply Changes]
    end
    
    subgraph InstallFlow[Install/Update Process]
        I1[Prepare Environment] --> I2[Run Migrations]
        I2 --> I3[Build Assets]
    end
    
    subgraph ClientFlow[Client Install Process]
        C1[Authenticate] --> C2[Sync Client Repo]
        C2 --> C3[Apply Config]
    end
    
    subgraph UtilsFlow[Utilities]
        U1[SSL Setup] & U2[Network Config] & U3[Debug Tools]
    end
    
    %% Styling
    classDef start fill:#4caf50,stroke:#2e7d32,color:white,stroke-width:2px
    classDef process fill:#2196f3,stroke:#0d47a1,color:white,stroke-width:2px
    classDef command fill:#673ab7,stroke:#311b92,color:white,stroke-width:2px
    classDef subgraphstyle fill:#f8f9fa,stroke:#666,stroke-width:1px,stroke-dasharray:3,3
    
    class Start start
    class Init,Parse process
    class Commands command
    class BuildFlow,StartFlow,GitFlow,InstallFlow,ClientFlow,UtilsFlow subgraphstyle
```

### 8.2. Command Execution Flow
```mermaid
sequenceDiagram
    participant User
    participant Runner
    participant Docker
    participant Git
    participant Submodules
    
    User->>+Runner: Execute Command
    
    alt Build Command
        Runner->>Docker: Build Images
        Docker-->>Runner: Build Status
    else Start Command
        Runner->>Docker: Start Containers
        Docker-->>Runner: Start Status
    else Git Sync
        Runner->>Git: Fetch Updates
        Git-->>Runner: Update Status
        Runner->>Submodules: Update Submodules
        Submodules-->>Runner: Update Status
    else Install/Update
        Runner->>Runner: Run Migrations
        Runner->>Runner: Build Assets
        Runner-->>User: Installation Complete
    else Client Install
        Runner->>Git: Clone Client Repo
        Git-->>Runner: Clone Status
        Runner->>Runner: Apply Config
        Runner-->>User: Client Setup Complete
    end
    
    Runner-->>-User: Command Result
```

## 9. Troubleshooting

### 9.1. Common Problems
- **Container Fails to Start**:
  - Check logs: `docker logs [container]`
  - Verify resource availability
  - Check port conflicts

- **Network Issues**:
  - Verify Docker network exists
  - Check firewall settings
  - Test container connectivity

- **Build Failures**:
  - Clear Docker cache: `docker system prune -a`
  - Check network connectivity
  - Verify build context

### 9.2. Debugging
- **Verbose Output**:
  - Add `-v` or `--verbose` flag
  - Set `DEBUG=1` environment variable

- **Inspection Commands**:
  - `docker ps -a`: List all containers
  - `docker network ls`: List networks
  - `docker volume ls`: List volumes

### 9.3. Getting Help
- **Documentation**:
  - `./runner.sh help`
  - `./runner.sh welcome`
  - Project README.md

- **Support**:
  - Check issue tracker
  - Contact maintainers
  - Community forums
