# Hướng Dẫn Sử Dụng Docker

## <PERSON><PERSON><PERSON>
- [Quản Lý Container](#quản-lý-container)
- [Quản Lý Image](#quản-lý-image)
- [Quản Lý Volume](#quản-lý-volume)
- [Quản Lý Network](#quản-lý-network)
- [Docker Compose](#docker compose)
- [Giám Sát và Gỡ Lỗi](#giám-sát-và-gỡ-lỗi)
- [Dọn Dẹp](#dọn-dẹp)
- [Lệnh <PERSON>](#lệnh-hữu-ích-khác)

---

## Quản Lý Container

### Khởi động và dừng
```bash
# Chạy một container
docker run -d --name my_container nginx

# Dừng container
docker stop my_container

# Khởi động lại container
docker restart my_container

# Tạm dừng container
docker pause my_container

# Tiếp tục container bị tạm dừng
docker unpause my_container
```

### Xem thông tin
```bash
# Liệt kê các container đang chạy
docker ps

# Liệt kê tất cả container (kể cả đã dừng)
docker ps -a

# Xem thông tin chi tiết container
docker inspect my_container

# Xem log container
docker logs my_container

# Xem log theo thời gian thực
docker logs -f my_container

# Xem tài nguyên sử dụng
docker stats

# Xem các process đang chạy trong container
docker top my_container
```

### Tương tác với container
```bash
# Vào container bằng shell
docker exec -it my_container sh
# hoặc
docker exec -it my_container bash
# hoặc
docker compose run --rm -w /var/dev <my_service> ash

# Copy file từ container ra host
docker cp my_container:/path/in/container /host/path

# Copy file từ host vào container
docker cp /host/path my_container:/path/in/container
```

## Quản Lý Image

### Tìm kiếm và tải image
```bash
# Tìm kiếm image trên Docker Hub
docker search nginx

# Tải image
docker pull nginx:latest

# Xem danh sách image
docker images
# hoặc
docker image ls
```

### Xóa image
```bash
# Xóa image
docker rmi nginx

# Xóa image không sử dụng
docker image prune

# Xóa tất cả image không sử dụng
docker image prune -a
```

## Quản Lý Volume

### Tạo và xem volume
```bash
# Tạo volume
docker volume create my_volume

# Xem danh sách volume
docker volume ls

# Xem thông tin volume
docker volume inspect my_volume

# Xóa volume
docker volume rm my_volume

# Xóa tất cả volume không sử dụng
docker volume prune
```

## Quản Lý Network

### Tạo và quản lý network
```bash
# Tạo network
docker network create my_network

# Xem danh sách network
docker network ls

# Kết nối container vào network
docker network connect my_network my_container

# Ngắt kết nối network
docker network disconnect my_network my_container

# Xem thông tin network
docker network inspect my_network

# Xóa network
docker network rm my_network
```

## Docker Compose

### Các lệnh cơ bản
```bash
# Khởi động service
docker compose up -d

# Dừng service
docker compose down

# Xem log
docker compose logs -f

# Khởi động lại service
docker compose restart

# Build lại image
docker compose build

# Xem các service đang chạy
docker compose ps
```

## Giám Sát và Gỡ Lỗi

### Kiểm tra hệ thống
```bash
# Xem thông tin hệ thống Docker
docker system df

# Xem sự kiện Docker
docker events

# Kiểm tra disk usage
docker system df -v

# Xem lịch sử image
docker history image_name
```

### Debug container
```bash
# Kiểm tra thay đổi file trong container
docker diff my_container

# Kiểm tra port đang lắng nghe
docker port my_container

# Export container thành file tar
docker export my_container > my_container.tar

# Import container từ file tar
cat my_container.tar | docker import - my_image:tag
```

## Dọn Dẹp

### Xóa Container
```bash
# Xóa container đã dừng
docker container prune

# Xóa container đã dừng và không sử dụng (không xóa volumes)
docker container prune -f

# Xóa tất cả container (đang chạy và dừng)
docker rm -f $(docker ps -aq)

# Hoặc
# docker container rm -f $(docker container ls -aq)
```

### Xóa Image
```bash
# Xóa image không có tag (dangling)
docker image prune

# Xóa tất cả image không sử dụng
docker image prune -a

# Xóa image cụ thể
docker rmi image_name:tag

# Xóa nhiều image theo pattern
docker images | grep "pattern" | awk '{print $3}' | xargs docker rmi -f

# Xóa tất cả image
docker rmi -f $(docker images -q)
```

### Xóa Volume
```bash
# Xóa volume không sử dụng
docker volume prune

# Xóa volume cụ thể
docker volume rm volume_name

# Xóa tất cả volume không sử dụng
docker volume prune -f

# Xóa tất cả volume
docker volume rm $(docker volume ls -q)
```

### Xóa Network
```bash
# Xóa network không sử dụng
docker network prune

# Xóa network cụ thể
docker network rm network_name

# Xóa tất cả network không sử dụng
docker network prune -f

# Xóa tất cả network không phải mặc định
docker network rm $(docker network ls --format '{{.Name}}' | grep -v "bridge\|host\|none")
```

### Xóa Build Cache
```bash
# Xóa builder cache
# Xóa tất cả build cache
docker builder prune -a -f

# Xóa build cache cũ hơn 24h
docker builder prune --filter "until=24h"

# Xóa build cache không sử dụng
docker builder prune -f
```

### Dọn Dẹp Hệ Thống
```bash
# Xóa tất cả container, network, image, volume không sử dụng
docker system prune -a --volumes

# Xóa container dừng, network không sử dụng, build cache
docker system prune

# Xóa tất cả mọi thứ (cẩn thận!)
docker system prune -a --volumes --remove-orphans
```

### Xóa Theo Điều Kiện
```bash
# Xóa container dừng hơn 1 tuần
docker container prune --filter "until=168h"

# Xóa image cũ hơn 2 tháng
docker image prune -a --filter "until=1440h"

# Xóa volume không được sử dụng bởi container nào
docker volume prune --filter "label!=keep"
```

### Kiểm Tra Dung Lượng Đã Giải Phóng
```bash
# Xem tổng dung lượng đã sử dụng
docker system df

# Xem chi tiết dung lượng
docker system df -v

# Xem tổng dung lượng image
docker system df --format '{{.Type}}: {{.Size}}' | grep 'Images'
```

## Lệnh Hữu Ích Khác

### Container
```bash
# Đổi tên container
docker rename old_name new_name

# Xem thay đổi file trong container
docker diff my_container

# Tạo image từ container
docker commit my_container new_image_name
```

### Log và Monitoring
```bash
# Xem tài nguyên sử dụng theo container
docker stats

# Xem tài nguyên sử dụng theo service (compose)
docker compose top

# Xem log của service
docker compose logs -f service_name
```

### Network
```bash
# Kiểm tra kết nối mạng giữa các container
docker network inspect bridge

# Kiểm tra port đang mở
docker port container_name
```

### Volume
```bash
# Backup volume
docker run --rm -v volume_name:/source -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz -C /source .

# Restore volume
docker run --rm -v volume_name:/target -v $(pwd):/backup alpine sh -c "cd /target && tar xzf /backup/backup.tar.gz"
```

### Docker Compose
```bash
# Scale service
docker compose up -d --scale web=3

# Xem log của một service cụ thể
docker compose logs -f service_name

# Chạy lệnh trong service
docker compose exec service_name command
```

### Xem thông tin hệ thống
```bash
# Xem thông tin Docker
docker info

# Xem version Docker
docker version

# Kiểm tra disk usage
docker system df
```

### Xử lý sự cố
```bash
# Kiểm tra event
docker events

# Kiểm tra process trong container
docker top container_name

# Kiểm tra thông tin mạng
docker network inspect bridge
```

### Quản lý container đang chạy
```bash
# Xem tài nguyên sử dụng của container
docker stats

# Xem thông tin CPU, memory
docker stats --no-stream

# Xem thông tin chi tiết container
docker container inspect container_name
```

### Sao lưu và phục hồi
```bash
# Lưu image thành file
docker save -o image.tar image_name

# Tải image từ file
docker load -i image.tar

# Export container thành file
docker export container_name > container.tar

# Import container từ file
cat container.tar | docker import - image_name:tag
```

### Xóa tất cả container và image
```bash
# Dừng và xóa tất cả container
docker stop $(docker ps -aq) && docker rm $(docker ps -aq)

# Xóa tất cả image
docker rmi $(docker images -q)

# Hoặc xóa mọi thứ (cả volume, network)
docker system prune -a --volumes
```

## Kết Luận
Tài liệu này cung cấp các lệnh Docker cơ bản đến nâng cao thường dùng trong quá trình phát triển và vận hành. Tùy theo nhu cầu cụ thể, bạn có thể kết hợp các lệnh này để tạo ra các tác vụ phức tạp hơn.
