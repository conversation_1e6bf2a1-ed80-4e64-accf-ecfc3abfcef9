# CSlant.net Command Flow Analysis

## Overview

The `cslant.net` command manages the core components of CSlant.net, including:
- Laravel application (Flant)
- WordPress system (wp-base, wp-core, wp-suite, wp-themes)
- Support tools (symlink, installation)

## Directory Structure

```
cslant.net/
├── flant/           # Laravel application
├── wp-base/         # Custom WordPress core
├── wp-core/         # Vanilla WordPress
├── wp-suite/        # Custom plugins
└── wp-themes/       # Custom themes
```

## Main Control Flow

### 1. Entry Point

```bash
./runner.sh cslant.net <subcommand> [options]
```

Execution starts from `runner.sh`:
1. Receives user command
2. Calls `cslant_net_handler` with corresponding parameters

### 2. Core Functions Analysis

#### 2.1. `cslant_net_handler` (handlers.sh)

Main function that processes all subcommands of `cslant.net`.

**Flow:**
1. Receives input parameters
2. Parses subcommand and routes to corresponding function
3. Handles errors for invalid commands

#### 2.2. Sync Functions

Located in `setup/git/cslant.net.sh`:

1. **`cslant_net_sync`**:
   - Takes repository identifier as parameter
   - Calls corresponding sync function
   - Supports force sync mode

2. **Specific Sync Functions**:
   - `cslant_net_flant_sync`: Syncs Laravel source code
   - `cslant_net_wp_base_sync`: Syncs WordPress base
   - `cslant_net_wp_core_sync`: Syncs WordPress core
   - `cslant_net_wp_suite_sync`: Syncs custom plugins
   - `cslant_net_wp_themes_sync`: Syncs custom themes

#### 2.3. `cslant_net_flant_source_implement` (handlers.sh)

Handles Laravel application (Flant) setup and updates:

1. **PHP Dependencies**:
   ```bash
   composer install/update
   cp -n .env.example .env
   ```

2. **Frontend Build**:
   ```bash
   npm install
   npm run build
   ```

#### 2.4. `cslant_net_wp_symlink` (express-web.sh)

Creates symlinks for WordPress plugins/themes:

1. Validates input parameters
2. Determines target path
3. Removes existing symlink if exists
4. Creates new symlink
   - Supports both Docker and local environments
   - Handles plugins and themes differently

## Command Processing Flows

### 1. Install WP-CLI
```mermaid
sequenceDiagram
    participant User
    participant Runner
    participant Handler
    
    User->>Runner: ./runner.sh cslant.net install wp-cli
    Runner->>Handler: cslant_net_handler("install", "wp-cli")
    Handler->>Handler: install_wp_cli()
    Handler-->>User: Installation result
```

### 2. Sync and Install Flant
```mermaid
sequenceDiagram
    participant User
    participant Handler
    participant Sync
    participant Docker
    
    User->>Handler: ./runner.sh cslant.net flant
    Handler->>Sync: cslant_net_sync("flant")
    Sync->>Sync: cslant_net_flant_sync()
    Sync-->>Handler: Sync completed
    Handler->>Handler: cslant_net_flant_source_implement("install")
    Handler->>Docker: docker_start_service_php_source()
    Docker-->>Handler: Service started
    Handler-->>User: Operation completed
```

### 3. Create Symlink for Plugin/Theme
```mermaid
sequenceDiagram
    participant User
    participant Handler
    participant Symlink
    
    User->>Handler: ./runner.sh cslant.net wp-suite-symlink plugin cslant-suite /path/to/wp
    Handler->>Symlink: cslant_net_wp_symlink('plugin', 'cslant-suite', '/path/to/wp')
    Symlink->>Symlink: Validate parameters
    Symlink->>Symlink: Remove existing symlink if exists
    Symlink->>Symlink: Create new symlink
    Symlink-->>Handler: Success
    Handler-->>User: Symlink created successfully
```

## Error Handling

1. **Parameter Validation**:
   - All functions validate input parameters
   - Shows usage instructions for invalid parameters

2. **Sync Error Handling**:
   - Checks network connectivity
   - Verifies repository access
   - Provides detailed error messages

3. **Symlink Error Handling**:
   - Verifies write permissions
   - Checks source directory existence
   - Provides clear error messages for symlink creation failures

## Development Notes

1. **Docker Environment**:
   - Ensure Docker is running
   - Verify proper access permissions

2. **File Permissions**:
   - Read/write access to required directories
   - Execute permissions for commands

3. **Environment Variables**:
   - Check .env file
   - Ensure required variables are defined

4. **Troubleshooting**:
   - Check detailed logs when errors occur
   - Verify dependent services are running
   - Use verbose mode for debugging

5. **Testing**:
   - Test in development environment first
   - Verify all dependencies are installed
   - Check for port conflicts

## Common Issues and Solutions

1. **Sync Failures**:
   - Verify Git credentials
   - Check network connectivity
   - Ensure sufficient disk space

2. **Permission Denied**:
   - Check file ownership
   - Verify directory permissions
   - Run with appropriate user privileges

3. **Docker Issues**:
   - Restart Docker service if needed
   - Check container logs
   - Verify Docker network configuration

4. **Build Failures**:
   - Check dependency versions
   - Verify build tools are installed
   - Review build logs for specific errors
