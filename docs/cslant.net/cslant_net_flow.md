# CSlant.net Command Flow Documentation

## Table of Contents
1. [Overview](#1-overview)
2. [Command Structure](#2-command-structure)
3. [Subcommands](#3-subcommands)
   - [install](#31-install)
   - [express-web](#32-express-web)
   - [flant](#33-flant)
   - [wp-base](#34-wp-base)
   - [wp-suite](#35-wp-suite)
   - [wp-themes](#36-wp-themes)
   - [wp-suite-symlink](#37-wp-suite-symlink)
   - [wp-themes-symlink](#38-wp-themes-symlink)
4. [Flant (Laravel) Setup](#4-flant-laravel-setup)
5. [Examples](#5-examples)

## 1. Overview
The `cslant.net` command is a handler for managing various components of the CSlant.net ecosystem, including WordPress installations, Laravel applications, and related resources.

## 2. Command Structure
```bash
./runner.sh cslant.net <subcommand> [options]
```

## 3. Subcommands

### 3.1. install
Installs specific components.

**Subcommands:**
- `wp-cli`: Installs WP-CLI (WordPress Command Line Interface)

**Example:**
```bash
./runner.sh cslant.net install wp-cli
```

### 3.2. express-web
Manages WordPress site creation and management through Express Web.

#### 3.2.1. Create New Site
**Command:**
```bash
./runner.sh cslant.net express-web create <slug>
```

**Parameters:**
- `<slug>`: Unique identifier for the site (e.g., 'my-site')

**Flow:**

1. **Command Routing**
   - `runner.sh` → `cslant_net_handler()` in `setup/handlers.sh`
   - `express_web()` in `setup/wordpress/express-web.sh` is called with `create` and `<slug>` arguments

2. **Prerequisites Check**
   - Verifies required repositories exist using `cslant_net_sync()` in `setup/git/cslant.net.sh`:
     - `wp-cslant-suite`
     - `wp-cslant-themes`
     - `cslant.net-wp-core`
   - Ensures WP-CLI is installed via `install_wp_cli()`
   - Validates environment variables in `express_web_create()`

3. **Site Cleanup**
   - `express_web_cleanup_site()` in `setup/wordpress/express-web.sh`:
     - Removes existing site directory at `$SOURCE_CODE_PATH/wordpress/${slug}`
     - Drops existing database using MySQL commands via Docker
   - Creates fresh directory structure with proper permissions

3. **WordPress Installation**
   - `express_web_create()` in `setup/wordpress/express-web.sh`:
     - Copies WordPress core files from `$SOURCE_DIR/cslant.net-wp-core` to `$site_dir`
     - Creates and configures MySQL database using Docker:
       ```bash
       docker compose run --rm -w /var/lib/mysql mysql bash -l -c "mysql -h mysql -u root -p$MYSQL_ROOT_PASSWORD -e 'CREATE DATABASE IF NOT EXISTS \`$db_name\`;'"
       ```
     - Runs WordPress installation via WP-CLI:
       ```bash
       wp core install --url="$real_url" --title="$site_title" --admin_user="$admin_user" --admin_password="$admin_password" --admin_email="$admin_email" --skip-email
       ```
     - Generates secure admin credentials using `openssl`

4. **Configuration**
   - `express_web_configure_site()` in `setup/wordpress/express-web.sh`:
     - Updates site options using `wp option update`
     - Installs and activates plugins via `wp plugin install --activate`
     - Sets up theme using `wp theme activate`
     - Creates symlinks using `cslant_net_wp_symlink()` for:
       - Plugins from `wp-cslant-suite`
       - Themes from `wp-cslant-themes`

5. **Finalization**
   - Sets file permissions using `chmod` and `chown`
   - Restarts web server if needed via Docker commands
   - Displays site information including:
     - Site URL
     - Admin credentials
     - Database connection details

**Output:**
- Site URL: `http(s)://<slug>.demo.cslant.net.local`
- Admin URL: `http(s)://<slug>.demo.cslant.net.local/wp-admin`
- Admin username
- Admin password (auto-generated)

**Example:**
```bash
# Create a new site with slug 'newsite'
./runner.sh cslant.net express-web create newsite
```

#### 3.2.2. List Sites
**Command:**
```bash
./runner.sh cslant.net express-web list
```

Displays all existing WordPress sites with their details.

### 3.3. flant
Manages the Flant Laravel application.

**Usage:**
```bash
./runner.sh cslant.net flant
```

**Actions:**
1. Syncs the Flant repository
2. Implements Flant source
3. Starts the PHP service

### 3.4. wp-base
Manages the WordPress base installation.

**Usage:**
```bash
./runner.sh cslant.net wp-base
```

**Actions:**
1. Syncs the wp-base repository
2. Starts the PHP service

### 3.5. wp-suite
Manages the WordPress suite.

**Usage:**
```bash
./runner.sh cslant.net wp-suite
```

**Actions:**
1. Syncs the wp-suite repository

### 3.6. wp-themes
Manages WordPress themes.

**Usage:**
```bash
./runner.sh cslant.net wp-themes
```

**Actions:**
1. Syncs the wp-themes repository

### 3.7. wp-suite-symlink
Creates symlinks for WordPress suite plugins.

**Usage:**
```bash
./runner.sh cslant.net wp-suite-symlink plugin <source> <target> [options]
```

**Parameters:**
- `plugin`: Type of symlink (always 'plugin' for this command)
- `<source>`: Source directory or file
- `<target>`: Target symlink location
- `[options]`: Additional options (optional)

### 3.8. wp-themes-symlink
Creates symlinks for WordPress themes.

**Usage:**
```bash
./runner.sh cslant.net wp-themes-symlink theme <source> <target> [options]
```

**Parameters:**
- `theme`: Type of symlink (always 'theme' for this command)
- `<source>`: Source directory or file
- `<target>`: Target symlink location
- `[options]`: Additional options (optional)

## 4. Flant (Laravel) Setup
The Flant setup process includes the following steps:

1. **Environment Setup**
   - Loads environment variables from the source directory

2. **PHP Dependencies**
   - Runs `composer install` or `composer update` based on the command
   - Copies `.env.example` to `.env` if it doesn't exist

3. **Frontend Build**
   - Installs NPM dependencies
   - Runs the build process

## 5. Examples

### Install WP-CLI
```bash
./runner.sh cslant.net install wp-cli
```

### Sync and Setup Flant
```bash
./runner.sh cslant.net flant
```

### Create a plugin symlink in wp-suite
```bash
./runner.sh cslant.net wp-suite-symlink plugin cslant-suite plugins/my-plugin
```

### Create a theme symlink in wp-themes
```bash
./runner.sh cslant.net wp-themes-symlink theme cslant-wp-1;  themes/my-theme
```

### Sync WordPress Base
```bash
./runner.sh cslant.net wp-base
```
