FROM dunglas/frankenphp

# add additional extensions here:
RUN install-php-extensions \
    pdo_mysql \
    gd \
    intl \
    zip \
    opcache \
    exif \
    fileinfo \
    imagick \
    sodium \
    xml \
    curl \
    mbstring \
    bcmath \
    mysqli

# Copy custom php.ini configuration
COPY php.ini $PHP_INI_DIR/php.ini

# Create the cslant user with specific UID, GID and home directory
RUN addgroup --gid 1001 cslant && \
    adduser --uid 1001 --gid 1001 cslant

# Install WP-CLI
RUN curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar && \
    chmod +x wp-cli.phar && \
    mv wp-cli.phar /usr/local/bin/wp
 
# Set the working directory and switch to cslant user
WORKDIR /var/www/demo.cslant.net
USER cslant