# Use an official PHP image that supports ARM64
FROM php:8.0-apache

# Install system dependencies for PHP extensions
RUN apt-get update && apt-get install -y \
        libfreetype6-dev \
        libjpeg62-turbo-dev \
        libpng-dev \
        libxml2-dev \
        libzip-dev \
        default-mysql-client \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure, install and enable PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd mysqli pdo pdo_mysql zip \
    && docker-php-ext-enable mysqli

# Install phpMyAdmin
ENV PHPMYADMIN_VERSION 5.1.1
RUN curl -o phpmyadmin.tar.gz -fSL "https://files.phpmyadmin.net/phpMyAdmin/${PHPMYADMIN_VERSION}/phpMyAdmin-${PHPMYADMIN_VERSION}-all-languages.tar.gz" \
    && tar -xzf phpmyadmin.tar.gz -C /var/www/html --strip-components=1 \
    && rm phpmyadmin.tar.gz \
    && chown -R www-data:www-data /var/www/html

# Configure phpMyAdmin
COPY config.inc.php /var/www/html/
COPY php.ini /usr/local/etc/php/conf.d/
