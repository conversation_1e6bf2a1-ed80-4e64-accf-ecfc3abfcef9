#!/bin/bash

# Check if domain parameter is provided
if [ -z "$1" ]; then
    echo "Error: Please provide a domain in the format {slug}.demo.cslant.net"
    echo "Usage: $0 {slug}.demo.cslant.net"
    exit 1
fi

# Extract slug from domain
DOMAIN="$1"
SLUG=$(echo "$DOMAIN" | cut -d'.' -f1)

# Check if domain is in correct format
if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+$ ]]; then
    echo "Error: Invalid domain format. Please use format: {slug}.demo.cslant.net"
    exit 1
fi

# Define paths
PIKACHU_DIR=$(pwd)
TARGET_DIR="/var/www/demo.cslant.net/${SLUG}"
MYSQL_USER="root"
MYSQL_PASS="root"
MYSQL_HOST="mysql"

# Generate a shorter username using first 8 chars of SHA-1 hash of the slug
USER_NAME="$(echo -n "$SLUG" | sha1sum | cut -c1-8)"
DB_USER="user_$USER_NAME"
DB_PASS=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9!@#$%^&*()_+' | head -c 16)
DB_NAME="wp_$USER_NAME"

# Check if directory already exists
if [ -d "$TARGET_DIR" ]; then
    echo "Error: Directory $TARGET_DIR already exists"
    exit 1
fi

# Database operations - using Docker Compose
docker compose exec -T mysql mysql -u root -proot <<MYSQL_SCRIPT
CREATE DATABASE IF NOT EXISTS \`$DB_NAME\`;
CREATE USER '$DB_USER'@'%' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
FLUSH PRIVILEGES;
MYSQL_SCRIPT

echo "Created database: $DB_NAME"
echo "Created MySQL user: $DB_USER"

##
# Start wordpress installation
#

# Create target directory
mkdir -p "$TARGET_DIR"

# Link WordPress core files
ln -s /var/www/wordpress-master/wp-admin "$TARGET_DIR/wp-admin"
ln -s /var/www/wordpress-master/wp-includes "$TARGET_DIR/wp-includes"
ln -s /var/www/wordpress-master/*.php "$TARGET_DIR"

# Create site-specific directories
mkdir "$TARGET_DIR/wp-content"
mkdir "$TARGET_DIR/wp-content/plugins"
mkdir "$TARGET_DIR/wp-content/uploads"
mkdir "$TARGET_DIR/wp-content/themes"

# Link shared plugins and themes
ln -s /var/www/wordpress-master/wp-content/plugins/* "$TARGET_DIR/wp-content/plugins/"
ln -s /var/www/cslant-themes/flatsome "$TARGET_DIR/wp-content/themes/flatsome"
ln -s /var/www/cslant-themes/woodmart "$TARGET_DIR/wp-content/themes/woodmart"

# Create wp-config.php using WP-CLI
if [ -f "$TARGET_DIR/wp-config.php" ]; then
    rm "$TARGET_DIR/wp-config.php"
fi
docker compose exec -T frankenphp wp config create \
    --path="$TARGET_DIR" \
    --dbname="$DB_NAME" \
    --dbuser="$DB_USER" \
    --dbpass="$DB_PASS" \
    --dbhost="mysql" \
    --dbprefix="wp_" \
    --skip-check \
    --force 

# Complete WordPress installation
echo "Running WordPress installation..."
WP_ADMIN_USER="admin_$USER_NAME"  # First 8 chars of slug for admin username
WP_ADMIN_EMAIL="admin@${DOMAIN}"
WP_ADMIN_PASS=$(openssl rand -base64 12 | tr -dc 'a-zA-Z0-9!@#$%^&*()_+' | head -c 16)

# Install WordPress using the existing FrankenPHP container with WP-CLI
docker compose exec -T frankenphp wp core install \
    --path="/var/www/demo.cslant.net/$SLUG" \
    --url="https://$DOMAIN" \
    --title="$DOMAIN" \
    --admin_user="$WP_ADMIN_USER" \
    --admin_password="$WP_ADMIN_PASS" \
    --admin_email="$WP_ADMIN_EMAIL" \
    --skip-email

echo "WordPress setup completed!"

# Activate flatsome theme
docker compose exec -T frankenphp wp theme activate woodmart --path="/var/www/demo.cslant.net/$SLUG"

# Activate wordpress-seo,woocommerce,contact-form-7,cslant-suite plugin
docker compose exec -T frankenphp wp plugin activate wordpress-seo --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate woocommerce --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate contact-form-7 --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate js_composer --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate revslider --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate woodmart-core --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate elementor --path="/var/www/demo.cslant.net/$SLUG"
docker compose exec -T frankenphp wp plugin activate cslant-suite --path="/var/www/demo.cslant.net/$SLUG"

# Create demo account, username: demo, password: demo@cslant, role: cslant_customer
docker compose exec -T frankenphp wp user <NAME_EMAIL> --user_pass="demo@cslant" --role=cslant_customer --path="/var/www/demo.cslant.net/$SLUG"

# Output summary
echo -e "\n=== WordPress Deployment Summary ==="
echo "Domain:           https://$DOMAIN"
echo "Admin URL:        https://$DOMAIN/wp-admin"
echo "Installation dir: $TARGET_DIR"
echo ""
echo "=== Database Credentials ==="
echo "Database:         $DB_NAME"
echo "Database User:    $DB_USER"
echo "Database Pass:    $DB_PASS"
echo ""
echo "=== WordPress Admin ==="
echo "Username:         $WP_ADMIN_USER"
echo "Password:         $WP_ADMIN_PASS"
echo "Email:            $WP_ADMIN_EMAIL"
echo ""
echo "=== Next Steps ==="
echo "1. Visit https://$DOMAIN to view your site"
echo "2. Log in at https://$DOMAIN/wp-admin"
echo "3. Change your password after first login"
