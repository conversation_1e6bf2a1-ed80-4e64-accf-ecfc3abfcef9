#!/bin/bash

sync_repo() {
  PLATFORM=$1
  REPO_NAME=$2
  BRANCH_NAME=$3
  IS_FORCE=${4:-}
  GITHUB_URL=${5:-}
  GITLAB_URL=${6:-}

  FORCE=0
  if [ "$IS_FORCE" = '-f' ] || [ "$IS_FORCE" = '--force' ]; then
    FORCE=1
  fi

  [ -z "$BRANCH_NAME" ] && BRANCH_NAME="main"

  REPO_JSON_FILE="$CONFIG_DIR/repos.json"

  REPO_GITLAB_FROM_CONFIG=$(get_gitlab_repo_path "$REPO_NAME" "$REPO_JSON_FILE")
  echo "  ∟ REPO_GITLAB_FROM_CONFIG: $REPO_GITLAB_FROM_CONFIG"
  [ -n "$REPO_GITLAB_FROM_CONFIG" ] && {
    GITLAB_URL="$GITLAB_SSH_URL/$REPO_GITLAB_FROM_CONFIG.git"
    echo "    ∟ Found GitLab repo: $GITLAB_URL"
  }

  REPO_GITHUB_FROM_CONFIG=$(get_github_repo_key "$REPO_NAME" "$REPO_JSON_FILE")
  [ -n "$REPO_GITHUB_FROM_CONFIG" ] && {
    GITHUB_URL="$GITHUB_SSH_URL/$REPO_GITHUB_FROM_CONFIG.git"
    echo "    ∟ Found GitHub repo: $GITHUB_URL"
  }

  [ -z "$GITHUB_URL" ] && GITHUB_URL="$GITHUB_SSH_URL/$REPO_NAME.git"
  [ -z "$GITLAB_URL" ] && GITLAB_URL="$GITLAB_SSH_URL/$REPO_NAME.git"

  echo "  ∟ GITHUB_URL: $GITHUB_URL"
  echo "  ∟ GITLAB_URL: $GITLAB_URL"

  # Always use GitHub repo name for local path
  REPO_PATH="$CSLANT_PATH/repo/$REPO_NAME"
  if [ -z "$REPO_PATH" ]; then
    [ "$PLATFORM" = "gitlab" ] && REPO_PATH="$CSLANT_PATH/repo/$(basename "$REPO_NAME")"
  fi

  create_repo "$REPO_NAME" "$GITHUB_URL" "$GITLAB_URL" "$PLATFORM" "$REPO_PATH"

  cd "$REPO_PATH" || return
  echo "  ∟ Syncing $REPO_NAME, branch $BRANCH_NAME, path: $REPO_PATH"

  git fetch --all --prune

  REMOTE_PRIMARY="origin"
  REMOTE_SECONDARY="gitlab"
  [ "$PLATFORM" = "gitlab" ] && { REMOTE_PRIMARY="gitlab"; REMOTE_SECONDARY="origin"; }

  # Detect if BRANCH_NAME is a tag (refs/tags/*)
  if [[ "$BRANCH_NAME" == refs/tags/* ]]; then
    TAG_NAME="${BRANCH_NAME#refs/tags/}"
    echo "📦 Syncing tag '$TAG_NAME' from $REMOTE_PRIMARY..."
    git fetch "$REMOTE_PRIMARY" tag "$TAG_NAME"
    git checkout "tags/$TAG_NAME"
    git tag -f "$TAG_NAME" "$REMOTE_PRIMARY/$TAG_NAME" 2>/dev/null || true
    git push --force "$REMOTE_SECONDARY" "refs/tags/$TAG_NAME:refs/tags/$TAG_NAME"
  else
    if [ "$FORCE" = 1 ]; then
        echo "🚨 Force syncing branch '$BRANCH_NAME' from $REMOTE_PRIMARY..."

        git fetch --prune "$REMOTE_PRIMARY"

        if git show-ref --quiet refs/heads/$BRANCH_NAME; then
            echo "  ∟ Branch $BRANCH_NAME already exists, force resetting..."
            git switch "$BRANCH_NAME"
            git fetch "$REMOTE_PRIMARY" "$BRANCH_NAME"
            git reset --hard "$REMOTE_PRIMARY/$BRANCH_NAME"
        else
            echo "  ∟ Branch $BRANCH_NAME does not exist, creating and resetting..."
            git switch -c "$BRANCH_NAME" "$REMOTE_PRIMARY/$BRANCH_NAME"
        fi

        git clean -fd

        # Force push to secondary remote
        git push --force "$REMOTE_SECONDARY" "$BRANCH_NAME"
    else
      echo "📦 Syncing (soft) branch '$BRANCH_NAME' from $REMOTE_PRIMARY..."

      # Create or reset local branch
      if git show-ref --quiet refs/heads/$BRANCH_NAME; then
        echo "  ∟ Branch $BRANCH_NAME already exists, checking for conflicts..."

        # Switch to the branch first
        git switch "$BRANCH_NAME"

        # Fetch latest from remote
        git fetch "$REMOTE_PRIMARY" "$BRANCH_NAME"

        # Check if local branch has diverged from remote
        LOCAL_COMMIT=$(git rev-parse HEAD)
        REMOTE_COMMIT=$(git rev-parse "$REMOTE_PRIMARY/$BRANCH_NAME")
        MERGE_BASE=$(git merge-base HEAD "$REMOTE_PRIMARY/$BRANCH_NAME" 2>/dev/null || echo "")

        if [ "$LOCAL_COMMIT" != "$REMOTE_COMMIT" ] && [ "$LOCAL_COMMIT" != "$MERGE_BASE" ]; then
          echo "  ⚠️  Local branch has diverged from remote. Creating backup..."

          # Create backup branch with timestamp
          BACKUP_BRANCH="${BRANCH_NAME}-backup-$(date +%Y%m%d-%H%M%S)"
          echo "  ∟ Creating backup branch: $BACKUP_BRANCH"
          git branch "$BACKUP_BRANCH" "$LOCAL_COMMIT"

          # Push backup branch to both remotes
          echo "  ∟ Pushing backup branch to $REMOTE_SECONDARY..."
          git push "$REMOTE_SECONDARY" "$BACKUP_BRANCH"

          echo "  ∟ Pushing backup branch to $REMOTE_PRIMARY..."
          git push "$REMOTE_PRIMARY" "$BACKUP_BRANCH" 2>/dev/null || echo "    ∟ Could not push backup to $REMOTE_PRIMARY (may not have write access)"

          echo "  ✅ Backup created: $BACKUP_BRANCH"
        fi

        # Now sync with remote (this will overwrite local changes)
        echo "  ∟ Syncing with remote $REMOTE_PRIMARY/$BRANCH_NAME..."
        git reset --hard "$REMOTE_PRIMARY/$BRANCH_NAME"

      else
        echo "  ∟ Branch $BRANCH_NAME does not exist, creating..."
        git switch -c "$BRANCH_NAME" "$REMOTE_PRIMARY/$BRANCH_NAME"
      fi

      # Push synced branch to secondary remote
      echo "  ∟ Pushing synced branch to $REMOTE_SECONDARY..."
      git push "$REMOTE_SECONDARY" "$BRANCH_NAME"
    fi
  fi

  echo ''
}

create_repo() {
  REPO_NAME=$1
  GITHUB_URL=${2:-}
  GITLAB_URL=${3:-}
  PLATFORM=${4:-}
  REPO_PATH=${5:-}

  echo "    ∟ REPO_PATH: $REPO_PATH"

  if [ ! -d "$REPO_PATH" ]; then
    echo "  ∟ Missing REPO_PATH for $REPO_NAME, cloning from $GITHUB_URL"
    git clone "$GITHUB_URL" "$REPO_PATH"

    if [ -n "$GITLAB_URL" ]; then
      echo "  ∟ Adding GitLab remote for $REPO_NAME"
      cd "$REPO_PATH" || return
      git remote add gitlab "$GITLAB_URL"
    fi
  fi

  # Ensure we have the latest from both remotes
  git remote | grep -q "origin" || git remote add origin "$GITHUB_URL"
  git remote | grep -q "gitlab" || git remote add gitlab "$GITLAB_URL"
}

sync_from_github() {
  REPO_NAME=$1
  BRANCH_NAME=$2
  FORCE=${3:-}

  sync_repo "github" "$REPO_NAME" "$BRANCH_NAME" "$FORCE"
}

sync_from_gitlab() {
  REPO_NAME=$1
  BRANCH_NAME=$2
  FORCE=${3:-}

  sync_repo "gitlab" "$REPO_NAME" "$BRANCH_NAME" "$FORCE"
}
