#!/bin/bash

# Global variables
declare DOMAIN TEMPLATE SLUG TARGET_DIR TEMPLATE_DIR DB_NAME DB_USER DB_PASS USER_EMAIL USER_PASSWORD ADMIN_EMAIL ADMIN_PASSWORD

# Main function
main() {
    validate_arguments "$@"
    setup_environment
    setup_database
    install_wordpress
    setup_users
    show_summary
}

# Validate input arguments
validate_arguments() {
    # Check if domain parameter is provided
    if [ -z "$1" ]; then
        echo "Error: Please provide a domain in the format {slug}.identifynation.com"
        show_usage
        exit 1
    fi

    # Check if template parameter is provided
    if [ -z "$2" ]; then
        echo "Error: Please provide a template name"
        show_usage
        exit 1
    fi

    # Extract and validate domain
    DOMAIN="$1"
    TEMPLATE="$2"
    SLUG=$(echo "$DOMAIN" | cut -d'.' -f1)

    if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+$ ]]; then
        echo "Error: Invalid domain format. Please use format: {slug}.identifynation.com"
        exit 1
    fi

    # User credentials
    USER_EMAIL="$5"
    USER_PASSWORD="$6"

    # Admin credentials
    ADMIN_EMAIL="$3"
    ADMIN_PASSWORD="$4"
}

# Show script usage
show_usage() {
    echo "Usage: $0 {slug}.identifynation.com {template_name} {admin_email} {admin_password} {user_email} {user_password}"
}

# Setup environment variables and paths
setup_environment() {
    PIKACHU_DIR=$(pwd)
    TARGET_DIR="/var/www/identifynation.com/${SLUG}"
    TEMPLATE_DIR="/var/www/demo.cslant.net/$TEMPLATE"
    
    # Generate database credentials
    USER_NAME=$(openssl rand -hex 8)
    DB_USER="trial_$USER_NAME"
    DB_PASS=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9!@#$%^&*()_+' | head -c 16)
    DB_NAME="trial_$USER_NAME"
    
    # Print all variables
    echo "PIKACHU_DIR: $PIKACHU_DIR"
    echo "TARGET_DIR: $TARGET_DIR"
    echo "TEMPLATE_DIR: $TEMPLATE_DIR"
    echo "USER_NAME: $USER_NAME"
    echo "DB_USER: $DB_USER"
    echo "DB_PASS: $DB_PASS"
    echo "DB_NAME: $DB_NAME"
    echo "USER_EMAIL: $USER_EMAIL"
    echo "USER_PASSWORD: $USER_PASSWORD"
    echo "ADMIN_EMAIL: $ADMIN_EMAIL"
    echo "ADMIN_PASSWORD: $ADMIN_PASSWORD"
    
    # Check if target directory exists
    if [ -d "$TARGET_DIR" ]; then
        echo "Error: Directory $TARGET_DIR already exists"
        exit 1
    fi
}

# Setup database and user
setup_database() {
    echo "Setting up database..."
    docker compose exec -T mysql mysql -u root -proot <<MYSQL_SCRIPT
CREATE DATABASE IF NOT EXISTS \`$DB_NAME\`;
CREATE USER '$DB_USER'@'%' IDENTIFIED BY '$DB_PASS';
GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
FLUSH PRIVILEGES;
MYSQL_SCRIPT

    echo "✓ Created database: $DB_NAME"
    echo "✓ Created MySQL user: $DB_USER"
}

# Install and configure WordPress
install_wordpress() {
    echo "Installing WordPress..."
    
    # Create directory structure
    mkdir -p "$TARGET_DIR"
    mkdir -p "$TARGET_DIR/wp-content/plugins"
    mkdir -p "$TARGET_DIR/wp-content/themes"
    
    # Link core files
    ln -sf /var/www/wordpress-master/wp-admin "$TARGET_DIR/wp-admin"
    ln -sf /var/www/wordpress-master/wp-includes "$TARGET_DIR/wp-includes"
    ln -sf /var/www/wordpress-master/*.php "$TARGET_DIR"
    
    # Copy and link site content
    cp -R "$TEMPLATE_DIR/wp-content/uploads" "$TARGET_DIR/wp-content/"
    ln -sf /var/www/wordpress-master/wp-content/plugins/* "$TARGET_DIR/wp-content/plugins/"
    ln -sf /var/www/cslant-themes/woodmart "$TARGET_DIR/wp-content/themes/woodmart"
    
    # Create wp-config.php
    [ -f "$TARGET_DIR/wp-config.php" ] && rm "$TARGET_DIR/wp-config.php"
    docker compose exec -T frankenphp wp config create \
        --path="$TARGET_DIR" \
        --dbname="$DB_NAME" \
        --dbuser="$DB_USER" \
        --dbpass="$DB_PASS" \
        --dbhost="mysql" \
        --dbprefix="wp_" \
        --skip-check \
        --force
    
    # Import database from template
    TEMPLATE_DB_NAME=$(grep "DB_NAME" "$TEMPLATE_DIR/wp-config.php" | cut -d"'" -f4)
    docker compose exec -T mysql mysqldump -u root -proot "$TEMPLATE_DB_NAME" > "$TEMPLATE_DIR/wp-content/$TEMPLATE_DB_NAME.sql"
    docker compose exec -T mysql mysql -u root -proot "$DB_NAME" < "$TEMPLATE_DIR/wp-content/$TEMPLATE_DB_NAME.sql"
    
    # Replace domain in database and files
    docker compose exec -T frankenphp wp search-replace "$TEMPLATE.demo.cslant.net" "$DOMAIN" --path="$TARGET_DIR"
    find "$TARGET_DIR/wp-content/uploads" -type f -name "*.css" -exec sed -i "s/$TEMPLATE.demo.cslant.net/$DOMAIN/g" {} \;
    
    echo "✓ WordPress installation completed"
}

# Setup WordPress users
setup_users() {
    echo "Setting up users..."
    
    # Admin user
    WP_ADMIN_USER="admin_$USER_NAME"
    WP_ADMIN_EMAIL="admin@${DOMAIN}"
    WP_ADMIN_PASS=$(openssl rand -base64 12 | tr -dc 'a-zA-Z0-9!@#$%^&*()_+' | head -c 16)
    
    # Customer account
    docker compose exec -T frankenphp wp user create customer "${USER_EMAIL}" \
        --user_pass="${USER_PASSWORD}" \
        --role=cslant_customer \
        --path="$TARGET_DIR"
    
    # Clean up demo user
    docker compose exec -T frankenphp wp user delete demo --path="$TARGET_DIR" --yes
    
    echo "✓ User setup completed"
}

# Show deployment summary
show_summary() {
    echo -e "\n=== WordPress Deployment Summary ==="
    echo "Domain:           https://$DOMAIN"
    echo "Admin URL:        https://$DOMAIN/wp-admin"
    echo "Installation dir: $TARGET_DIR"
    echo ""
    echo "=== Database Credentials ==="
    echo "Database:         $DB_NAME"
    echo "Database User:    $DB_USER"
    echo "Database Pass:    $DB_PASS"
    echo ""
    echo "=== WordPress Admin ==="
    echo "Username:         $WP_ADMIN_USER"
    echo "Password:         $WP_ADMIN_PASS"
    echo "Email:            $WP_ADMIN_EMAIL"
    echo ""
    echo "=== Customer Account ==="
    echo "Username:         customer"
    echo "Password:         123456"
    echo "Email:            customer@${DOMAIN}"
    echo ""
    echo "=== Next Steps ==="
    echo "1. Visit https://$DOMAIN to view your site"
    echo "2. Log in at https://$DOMAIN/wp-admin"
    echo "3. Change your password after first login"
}

# Run main function
main "$@"
