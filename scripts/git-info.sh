#!/bin/bash

# Load repository configurations from JSON
get_gitlab_repo_path() {
  if [ -f "$2" ]; then
    repo_path=$(cat "$2" | grep -o "\"$1\": *\"[^\"]*\"" | cut -d '"' -f 4)
    echo "$repo_path"
  else
    echo "$1"
  fi
}

# Get GitHub repository path from config
get_github_repo_key() {
  if [ -f "$2" ]; then
    repo_key=$(cat "$2" | grep -o "\"[^\"]*\": *\"$1\"" | cut -d '"' -f 2)
    echo "$repo_key"
  else
    echo "$1"
  fi
}

# Get username mapping
get_mapped_username() {
  local username=$1
  if [ -f "$CONFIG_DIR/usernames.json" ]; then
    mapped_name=$(cat "$CONFIG_DIR/usernames.json" | grep -o "\"$username\": *\"[^\"]*\"" | cut -d '"' -f 4)
    if [ -n "$mapped_name" ]; then
      echo "$mapped_name"
    else
      echo "$username"
    fi
  else
    echo "$username"
  fi
}

# List available repositories from config
list_repos() {
  if [ -f "$CONFIG_DIR/repos.json" ]; then
    echo "Available repositories from config:"
    cat "$CONFIG_DIR/repos.json" | grep -o "\"[^\"]*\": *\"[^\"]*\"" | cut -d '"' -f 2
  else
    echo "No repository configuration found."
  fi
}
