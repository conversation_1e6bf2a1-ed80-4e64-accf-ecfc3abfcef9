{
    log {
        output stdout
        format json
    }
    servers {
        timeouts {
            read_body   10s
            read_header 5s
            write       300s
            idle        10m
        }
    }
}

# Main domain
demo.cslant.net {
    root * /var/www/demo.cslant.net/main
    tls /etc/letsencrypt/live/demo.cslant.net/fullchain.pem /etc/letsencrypt/live/demo.cslant.net/privkey.pem
    php_server
    file_server
}

# Wildcard subdomains
*.demo.cslant.net {
    tls /etc/letsencrypt/live/demo.cslant.net/fullchain.pem /etc/letsencrypt/live/demo.cslant.net/privkey.pem
    @subdomain {
        header_regexp host Host ^([^.]+)\.demo\.cslant\.net$
    }
    
    # Check Referer header - only allow requests from cslant.net
    @not_from_cslant {
        not header_regexp Referer ^https?://([^/]+\.)?cslant\.net(/|$)
    }
    # redir @not_from_cslant https://cslant.net/xem-demo?demo_url=https://{host} 302
    
    root @subdomain /var/www/demo.cslant.net/{http.regexp.1}
    php_server
    file_server
}

identifynation.com {
    respond "hello" 
}

*.identifynation.com {
    tls /etc/letsencrypt/live/identifynation.com/fullchain.pem /etc/letsencrypt/live/identifynation.com/privkey.pem
    @subdomain {
        header_regexp host Host ^([^.]+)\.identifynation\.com$
    }
    root @subdomain /var/www/identifynation.com/{http.regexp.1}
    php_server
    file_server
}

# Force HTTPS redirect
http://demo.cslant.net, http://*.demo.cslant.net, http://identifynation.com, http://*.identifynation.com {
    redir https://{host}{uri} permanent
}