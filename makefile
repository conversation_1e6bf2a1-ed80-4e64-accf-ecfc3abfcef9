### <PERSON><PERSON> helper commands
help:
	./runner.sh help

ssl:
	./runner.sh ssl

### Docker Compose commands
dc-up-b:
	docker compose up -d --build
dc-up:
	docker compose up -d
dc-down:
	docker compose down
dc-logs:
	docker compose logs -f
dc-nginx:
	docker compose down nginx && docker compose up -d nginx
dc-services:
	docker compose down $(services) && docker compose up -d $(services)

### In house - Local Development
inhouse-api-docs:
	./runner.sh si api-docs
inhouse-docs:
	./runner.sh si docs
inhouse-home:
	./runner.sh si home
inhouse-blog:
	./runner.sh si blog
inhouse-reports:
	./runner.sh si reports

### cslant.net - Local Development
REPO ?= $(if $(repo),$(repo), \
         $(action))
name ?= $(if $(name),$(name), \
         $(if $(project),$(project), \
		 $(if $(title),$(title), \
		 $(repo), \
		 'all'))))
cslant.net:
	./runner.sh cslant.net $(REPO) $(name)
cslant.net-flant:
	./runner.sh cslant.net flant

cslant.net-wp-suite-map:
	./runner.sh cslant.net wp-suite && ./runner.sh cslant.net wp-suite-symlink 'no-force' 'cslant-suite' $(path) $(is_docker)
cslant.net-wp-themes-map:
	./runner.sh cslant.net wp-themes && ./runner.sh cslant.net wp-themes-symlink 'no-force' $(name) $(path)

cslant.net-wp-themes-map-docker:
	./runner.sh cslant.net wp-themes && ./runner.sh cslant.net wp-themes-symlink 'no-force' $(name) $(slug) 1
cslant.net-wp-themes-map-all-docker:
	./runner.sh cslant.net wp-themes && ./runner.sh cslant.net wp-themes-symlink 'no-force' all $(slug) 1

cslant.net-express-web-create:
	./runner.sh cslant.net express-web create $(slug)
cslant.net-express-web-sync-dependencies:
	./runner.sh cslant.net wp-suite -f && ./runner.sh cslant.net wp-themes -f && ./runner.sh cslant.net wp-core -f

### Freelancer - Outsource Projects
start-ent-vws:
	./runner.sh ent vws start
start-ci-comic-news:
	./runner.sh ci comic-news start

