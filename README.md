# cl-tien-comic-news-scripts

The `cl-tien-comic-news-scripts` repository contains scripts for setting up and managing the Tien Comic News system.

It's need to be used in conjunction with the `docslant` repository, which contains the main application code.

Docslant repo: https://github.com/cslant/docslant

## Command Line Interface (CLI)

The CLI is a command line tool that allows you to interact with the Tien Comic News system. It provides various commands for managing the system, including:

| Command    | Description                                              |
|------------|----------------------------------------------------------|
| `first`    | Initializes the Tien Comic News system.                  |
| `git-sync` | Synchronizes the Git repository with the latest changes. |
| `in`       | Installs the PHP and Node.js dependencies.               |
| `start`    | Starts the Tien Comic News system.                       |

Run with prefix at `docslant` repo:

```bash
./runner.sh ci comic-news <command>
```

## Steps

1. **Initialize the system**: Run the `first` command to set up the Tien Comic News system.

    ```bash
    ./runner.sh ci comic-news first
    ```
   
2. **Synchronize the Git repository**: Use the `git-sync` command to synchronize the Git repository with the latest changes.

    ```bash
    ./runner.sh ci comic-news git-sync
    ```
   
3. **Install** the PHP and Node.js dependencies: Use the `in` command to install all the necessary dependencies for the system.

    > If you use Mac with arm64, you need to set the default platform to `linux/amd64`:
    > 
    > ```
    > export DOCKER_DEFAULT_PLATFORM=linux/amd64
    > ```

    Then, run the following command to install the dependencies:

    ```bash
    ./runner.sh ci comic-news in
    ```
   
4. **Start** the Tien Comic News system: Use the `start` command to start the system.

    ```bash
    ./runner.sh ci comic-news start
    ```

---

> Note: If you want to go to the node22 container, you can use the following command:
>
> ```bash
> docker compose run --rm -w /var/dev node22 ash
> ```
