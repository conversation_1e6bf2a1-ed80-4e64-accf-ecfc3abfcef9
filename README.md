# Pikachu - PHP Application with FrankenPHP

A modern PHP application stack using FrankenPHP, MySQL, and Caddy with automated SSL certificates via Let's Encrypt and Cloudflare DNS validation.

## Features

- **FrankenPHP**: High-performance PHP application server
- **MySQL 8.0**: Relational database
- **Caddy**: Modern web server with automatic HTTPS
- **Let's Encrypt**: Free SSL certificates with automatic renewal
- **Docker Compose**: Easy setup and deployment
- **Cloudflare DNS**: DNS-01 challenge for wildcard certificates

## Prerequisites

- Docker and Docker Compose installed on your system
- Cloudflare account with API access
- Domain name with Cloudflare as DNS provider

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pikachu
   ```

2. **Configure Environment**
   - Copy `.env.example` to `.env` and update the values:
     ```bash
     cp .env.example .env
     ```
   - Edit the `.env` file with your configuration

3. **Set up Cloudflare API**
   - Create a Cloudflare API token with DNS:Edit permissions
   - Update `cloudflare.ini` with your Cloudflare API token

4. **Start the services**
   ```bash
   docker-compose up -d
   ```

## Project Structure

- `docker-compose.yml`: Defines all services (FrankenPHP, MySQL, Certbot)
- `frankenphp/Dockerfile`: Custom FrankenPHP image with PHP extensions
- `Caddyfile`: Caddy web server configuration
- `cloudflare.ini`: Cloudflare API credentials for DNS validation
- `letsencrypt/`: Directory for SSL certificates (auto-created)

## Available Services

- **Web Server**: http://localhost:80
- **MySQL**:
  - Host: `mysql`
  - Port: 3306
  - User: `cslan`
  - Database: `cslan`

## SSL Certificates

The project is configured to automatically obtain and renew Let's Encrypt SSL certificates using DNS-01 challenge with Cloudflare. The certificates are stored in the `letsencrypt` directory.

## Troubleshooting

- **Certificate Issues**: Check the logs with `docker-compose logs certbot`
- **Container Not Starting**: Verify ports 80 and 443 are available
- **Database Connection**: Ensure the MySQL service is running before FrankenPHP starts

## Maintenance

To renew certificates manually:
```bash
docker-compose run --rm certbot renew
```

## Security Considerations

- Never commit sensitive information (API keys, passwords) to version control
- Keep your Cloudflare API token secure
- Regularly update your Docker images for security patches

## License

[Specify your license here]

## Support

For support, please contact [Your Contact Information]
