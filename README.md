# ent-vnwebsubmit-scripts

The `ent-vnwebsubmit-scripts` repository contains scripts for setting up and managing the Ent VN Web Submit system. 

It's need to be used in conjunction with the `docslant` repository, which contains the main application code.

Docslant repo: https://github.com/cslant/docslant

## Command Line Interface (CLI)

The CLI is a command line tool that allows you to interact with the Ent VN Web Submit system. It provides various commands for managing the system, including:

| Command           | Description                                                    |
|-------------------|----------------------------------------------------------------|
| `first`, `f`      | Initializes the Ent VN Web Submit system.                      |
| `all`, `a`        | Runs all the commands in the specified order.                  |
| `git-sync`, `gs`  | Synchronizes the Git repository with the latest changes.       |
| `storage`, `st`   | Sets up the storage system for the Ent VN Web Submit homepage. |
| `in`              | Installs the PHP and Node.js dependencies.                     |
| `start`, `s`      | Starts the Ent VN Web Submit system.                           |
| `import-db`, `id` | Imports the database from a backup file.                       |
| `export-db`, `ed` | Exports the database to a backup file.                         |

Run with prefix at `docslant` repo:

```bash
./runner.sh ent vws <command>
```

## Steps

First, go to the `docslant` repo and run the following command to set up the environment.

And then, run below commands:

1. **Initialize the system**: Run the `first` command to set up the Ent VN Web Submit system.

   ```bash
   ./runner.sh ent vws first
   ```

2. **Synchronize the Git repository**: Use the `git-sync` command to synchronize the Git repository with the latest changes.

    ```bash
    ./runner.sh ent vws git-sync
    ```

3. **Set up the storage system**: Run the `storage` command to set up the storage system for the Ent VN Web Submit homepage.

    ```bash
    ./runner.sh ent vws storage
    ```

4. **Install** the PHP and Node.js dependencies: Use the `all` command to install all the necessary dependencies for the system.
    If you use Mac with arm64, you need to set the default platform to `linux/amd64`:

    ```
    export DOCKER_DEFAULT_PLATFORM=linux/amd64
    ```

   Then, run the following command to install the dependencies:

    ```bash
    #create network
    ./runner.sh n
    #run
    ./runner.sh ent vws in
    ```

   > Note: This command will run all the commands in the specified order, including `first`, `git-sync`, and `storage`.
   
5. **Start the system**: Use the `start` command to start the Ent VN Web Submit system.

    ```bash
    ./runner.sh ent vws start
    ```
   
6. **Import the database**: If you need to import a database from a backup file, use the `import-db` command.

    ```bash
    ./runner.sh ent vws import-db all
    ```

7. Then, **update url for homepage wordpress** in vws-wp database.

![img.png](assets/img.png)

![img_1.png](assets/img_1.png)

![img.png](assets/img3.png)

> Note: If you want to go to the node22 container, you can use the following command:
> 
> ```bash
> docker compose run --rm -w /var/dev node22 ash
> ```
> 
> Get ID container of mysql:
> ```bash
> docker ps -a
> ```
> 
> Get IP of mysql container:
> ```bash
> docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' <container_id>
> ```
> 
> ---
>
> WordPress homepage admin account:
> 
> https://vietnamwebsummit.com/ajinomoto
> 
> Or:
> 
> http://ent.vws.cslant.com.local:81/wp-login.php
> ```
> tan.nguyen
> tan.nguyen#123T
> ```
> 
> Admin networking tool (/networking/ajinomoto/agendas):
> ```
> <EMAIL>
> password
> ```
> 
> ---
> 
> Admin http://ent.vws.meetup-cms.cslant.com.local:81
> ```
> admin
> 123456
> ```
> 
> If you can't login to the admin page, you can reset the password by this file: 
> `/Users/<USER>/Data/CSlant/source/vietnamwebsubmit/vws-meetup-cms/application/config/config.php`. Check line `522->546`
> 
> ![img.png](assets/img4.png)
> 
> Wtf authen style =)) 
> 
> ---
> 
> For homepage, You neeed to update memory limit in php.ini file(Min 4GB):
> 
> ```bash
> max_execution_time = 900
> max_input_time = 900
> memory_limit = 4096M
> post_max_size = 4096M
> 
> ; Maximum allowed size for uploaded files.
> ; http://php.net/upload-max-filesize
> upload_max_filesize = 4096M
>
> ; Maximum number of files that can be uploaded via a single request
> max_file_uploads = 2000
> ```
> 
> ---
> 
> Add cron job to wp-cron:
> 
> ```bash
> * * * * * /usr/bin/php7.4 /home/<USER>/vietnamwebsummit.com/vietnamwebsubmit/vws-homepage/wp-cron.php > /dev/null 2>&1
> ```
