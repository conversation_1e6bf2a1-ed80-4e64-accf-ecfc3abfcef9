# git-sync-config

This is a script to sync the git repositories for the CSlant project. It will clone the repositories from GitHub and GitLab, and set up the necessary environment variables.

Also, it will use with `git-info` in the `docslant` repo to sync the git repositories for the CSlant project.

Please note that this script is intended to be used with the `docslant` repo, and it will not work if you run it directly in the `git-info` repo.
