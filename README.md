```text
██████╗  ██████╗  ██████╗███████╗██╗      █████╗ ███╗   ██╗████████╗
██╔══██╗██╔═══██╗██╔════╝██╔════╝██║     ██╔══██╗████╗  ██║╚══██╔══╝
██║  ██║██║   ██║██║     ███████╗██║     ███████║██╔██╗ ██║   ██║   
██║  ██║██║   ██║██║     ╚════██║██║     ██╔══██║██║╚██╗██║   ██║   
██████╔╝╚██████╔╝╚██████╗███████║███████╗██║  ██║██║ ╚████║   ██║   
╚═════╝  ╚═════╝  ╚═════╝╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   
```

# CSlant system docker installer

This repo is to set up the CSlant DEV ENV with Docker.

We can use this runner to update all sources for development.

## Docker Hub

[CSlant Docker Hub](https://hub.docker.com/r/cslant)

In this docker repository, we have built the following images:

- [cslant/dev-php56](https://hub.docker.com/r/cslant/dev-php56)
- [cslant/dev-php74](https://hub.docker.com/r/cslant/dev-php74)
- [cslant/dev-php83](https://hub.docker.com/r/cslant/dev-php83)
- [cslant/dev-worker83](https://hub.docker.com/r/cslant/dev-worker83)
- [cslant/dev-php84](https://hub.docker.com/r/cslant/dev-php84)
- [cslant/dev-worker84](https://hub.docker.com/r/cslant/dev-worker84)
- [cslant/dev-mysql57](https://hub.docker.com/r/cslant/dev-mysql57)
- [cslant/dev-mysql](https://hub.docker.com/r/cslant/dev-mysql)
- [cslant/dev-node22](https://hub.docker.com/r/cslant/dev-node22)
- [cslant/dev-nginx](https://hub.docker.com/r/cslant/dev-nginx)
- [cslant/dev-postgres](https://hub.docker.com/r/cslant/dev-postgres)
- [cslant/dev-mailhog](https://hub.docker.com/r/cslant/dev-mailhog)

---

## Prerequisites

First, copy the `.env.example` file to `.env` and update the values.

```bash
envsubst < .env.example > .env
```

If you don't have `envsubst` command, you can use the following command:

```bash
cp .env.example .env
```

## Installation

In the `.env` file, update the values to match your environment.

```dotenv
# .env file
# ...

# Path to your code folder
SOURCE_CODE_PATH=/Users/<USER>/CSlant/sources

GITHUB_SSH_URL="**************:cslant"
GITHUB_TOKEN="<YOUR_GITHUB_TOKEN>"

GITLAB_SSH_URL="******************"
GITLAB_TOKEN="<YOUR_GITLAB_TOKEN>"
## DOMAIN SETTING
CSLANT_DOMAIN=cslant.com.local
API_DOMAIN=api.cslant.com.local
CSMS_DOMAIN=csms.cslant.com.local
BLOG_API_DOMAIN=api.blog.cslant.com.local
BLOG_ADMIN_DOMAIN=admin.blog.cslant.com.local
...

BLOG_ADMIN_DIR=hello

BLOG_FE_COMMAND=dev
HOME_FE_COMMAND=dev
HOME_FE2_COMMAND=dev
```

> [!IMPORTANT]
> ### Command can't be used if wrong values are set in the `.env` file.
> 
> 1. If the `SOURCE_CODE_PATH` is wrong, the runner will not be able to find the source code. So, please make sure the `SOURCE_CODE_PATH` is correct.
>
>       - So please get the full path of the `SOURCE_CODE_PATH` with the following command:
> 
>       ```bash
>       pwd
>       ```
>       Explanation:
>
>           - The SOURCE_CODE_PATH is the path to the source code folder. All needed repositories will be cloned to this folder. 
>           - You can set the SOURCE_CODE_PATH to any folder you want.
> 
> 2. Ensure the `GITHUB_SSH_URL`, `GITHUB_TOKEN`, `GITLAB_SSH_URL` and `GITLAB_TOKEN` are correct. If the values are wrong, the runner will not be able to sync the repositories.
> 
>       - Please get `GITHUB_TOKEN` from [here](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic).
>       - And get `GITLAB_TOKEN` from [here](https://docs.gitlab.com/user/profile/personal_access_tokens/).

### 💻 Start in Mac with arm64

**To run as amd64. You need to set the default platform to `linux/amd64`:**

```shell
export DOCKER_DEFAULT_PLATFORM=linux/amd64
```

Or, you can enable the environment variable in the `.env` file.

```dotenv
# .env file
# ...

# Set default platform to amd64
DOCKER_DEFAULT_PLATFORM=linux/amd64
```

### Run the runner to build the base images

Then, you can just run the following command to start the runner.

```bash
bash runner.sh b
```

The above command will run all the commands in the runner and start `nginx`, `php83`, `node22`, `mysql` and `elasticsearch`, and other services.

### Update a host file

Add the following lines to the `/etc/hosts` file:

```bash
127.0.0.1       cslant.com.local v1-cslant.com.local api.cslant.com.local csms.cslant.com.local blog.cslant.com.local admin.blog.cslant.com.local api.blog.cslant.com.local laravel.blog.cslant.com.local reports.cslant.com.local docs.cslant.com.local api-docs.cslant.com.local assets.cslant.com.local

127.0.0.1       cslant.net.local

127.0.0.1       outsrc.tien.news.cslant.com.local ent.vws.cslant.com.local ent.vws.meetup-cms.cslant.com.local
```

If you're using another domain, please update the domain in the `.env` file and update the domain in the `/etc/hosts` file as well.

### Set up the custom workspaces

⚠️⚠️⚠️ At here step. **YOU NEED TO WAIT FOR A WHILE**. ⚠️⚠️⚠️

**CHECK YOUR REQUIRED SERVICES BASED ON YOUR ROLE, YOUR PROJECTS OR YOUR WORKSPACE.** (Please make sure you have the correct permissions to access the repositories and ask your lead to add you to the required teams.)

> Why ??? 🥲 Why ???
> 
>Each project, each workspace has its own services. So, you need to set up the custom workspaces based on your role, your projects or your workspace.
> 
> It's not necessary to set up all the services. You can just set up the services that you need. **Great for your development environment**.
> 
> **Please be sure to ask your lead what you need to do**.

So...

If you want to start all the services, you can use the following command:

```bash
bash runner.sh a
bash runner.sh sa
```

---

## Usage

The runner has the following commands:

| Command                   | Description                              |
|---------------------------|------------------------------------------|
| `help`, `h`               | Shows the help message                   |
| `build`, `b`              | Builds the base src with Docker          |
| `build_all`, `ba`         | Builds all base src services with Docker |
| `network`, `n`            | Creates the Docker network               |
| `blog_git_sync`, `bgs`    | Syncs the blog repositories              |
| `home_git_sync`, `hgs`    | Syncs the home repositories              |
| `special_install`, `si`   | Install special src dependencies         |
| `start`, `s`              | Starts the base services in Docker       |
| `start_all`, `sa`         | Starts all base services in Docker       |
| `install`, `i`            | Install all base dependencies            |
| `update`, `u`             | Update all base dependencies             |
| `resource`, `r`           | Download base resources                  |
| `es_import`, `ei`         | Import data to Elasticsearch             |
| `all`, `a`                | Runs all the commands                    |
| `docker_sync_repo`, `dsr` | Syncs the repositories in Docker         |

To run a specific command, use the following command:

```bash
bash runner.sh <command>
```

For example, to run the `help` command to show the help message, use the following command:

```bash
bash runner.sh help
```

---

## Using Makefile commands

You can also use the provided `makefile` for common tasks. Here are some useful make commands:

### Docker Compose Commands

| Command                                | Description                                  |
|----------------------------------------|----------------------------------------------|
| `make help`                            | Show help message (same as `runner.sh help`) |
| `make ssl`                             | Generate SSL certificates                    |
| `make dc-up-b`                         | Start Docker Compose with build              |
| `make dc-up`                           | Start Docker Compose                         |
| `make dc-down`                         | Stop Docker Compose                          |
| `make dc-logs`                         | Show Docker Compose logs                     |

### Inhouse Projects Commands

| Command                                | Description                                  |
|----------------------------------------|----------------------------------------------|
| `make inhouse-api-docs`                | Install in-house API docs dependencies       |
| `make inhouse-docs`                    | Install in-house docs dependencies           |
| `make inhouse-home`                    | Install in-house home dependencies           |
| `make inhouse-blog`                    | Install in-house blog dependencies           |

### CSlant.net Commands

| Command                                | Description                                  |
|----------------------------------------|----------------------------------------------|
| `make cslant.net`                      | Run cslant.net command with params           |
| `make cslant.net repo=xxx`             | Run cslant.net for a specific repo           |
| `make cslant.net install=xxx name=xxx` | Run cslant.net with a specific install param |

### Client - Enterprise Projects Commands

| Command                                | Description                                  |
|----------------------------------------|----------------------------------------------|
| `make start-ent-vws`                   | Start ent-vws project                        |
| `make start-ci-comic-news`             | Start ci-comic-news project                  |

You can run any of these commands with:

```bash
make <command>
```

---

## Backup database in Docker

Backup postgres database to a SQL file:

```bash
pg_dump -U username -h hostname database_name >> /path/to/backup.sql
```

Example in this Docker:

```bash
pg_dump -U root -h localhost cslant_dev >> /docker-entrypoint-initdb.d/cslant_dev.sql
```

## Restore database in Docker

```bash
psql -U root -h localhost cslant_dev < /docker-entrypoint-initdb.d/cslant_dev.sql
```

## Run migration and seeding scripts for cslant-net database.

```bash
docker compose run --rm -w /var/dev php84 ash
```

```bash
php artisan migrate
php artisan db:seed
# or
php artisan migrate:fresh --seed --force
```
