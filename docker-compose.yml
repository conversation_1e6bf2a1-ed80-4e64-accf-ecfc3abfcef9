services:
  frankenphp:
    build:
      context: .
      dockerfile: frankenphp/Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - frankenphp_data:/data
      - frankenphp_config:/config
      - ./Caddyfile:/etc/frankenphp/Caddyfile
      - ./letsencrypt:/etc/letsencrypt:ro
      - ./sources:/sources
      - /var/www/demo.cslant.net:/var/www/demo.cslant.net
      - /var/www/identifynation.com:/var/www/identifynation.com
      - /var/www/wordpress-master:/var/www/wordpress-master
      - /var/www/cslant-themes:/var/www/cslant-themes
    depends_on:
      - certbot_1
      - certbot_2
      - mysql
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_USER: cslant
      MYSQL_PASSWORD: cslant
      MYSQL_ROOT_PASSWORD: root
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app-network

  certbot_1:
    image: certbot/dns-cloudflare:latest
    volumes:
      - ./letsencrypt:/etc/letsencrypt
      - ./cloudflare.ini:/etc/cloudflare.ini:ro
    command: >
      certonly --dns-cloudflare
        --dns-cloudflare-credentials /etc/cloudflare.ini
        --email <EMAIL>
        --agree-tos --non-interactive
        -d demo.cslant.net -d "*.demo.cslant.net"
    networks:
      - app-network

  certbot_2:
    image: certbot/dns-cloudflare:latest
    volumes:
      - ./letsencrypt:/etc/letsencrypt
      - ./cloudflare.ini:/etc/cloudflare.ini:ro
    command: >
      certonly --dns-cloudflare
        --dns-cloudflare-credentials /etc/cloudflare.ini
        --email <EMAIL>
        --agree-tos --non-interactive
        -d identifynation.com -d "*.identifynation.com"
    networks:
      - app-network

volumes:
  mysql_data:
  frankenphp_data:
  frankenphp_config:

networks:
  app-network:
    driver: bridge