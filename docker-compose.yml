networks:
  cslant_dev:
    external: true

volumes:
  postgres:
    driver: local
  pgadmin:
    driver: local
  elasticsearch:
    driver: local
  mysql:
    driver: local
  mysql57:
    driver: local
  mysql9:
    driver: local

services:
  ## WEB SERVICE
  nginx:
    container_name: "${COMPOSE_PROJECT_NAME}-nginx"
    image: cslant/dev-nginx
    volumes:
      - ./nginx/templates:/etc/nginx/templates
      - ./nginx/server/certs:/var/www/certs
      - ./nginx/conf/customs:/etc/nginx/customs
      - ${LOG_PATH}/nginx:/var/log/nginx
      - ${LOG_PATH}/clients:/var/log/clients
      - ${SOURCE_CODE_PATH}:/var/dev
    ports:
      - "${NGINX_HOST_HTTP_PORT:-80}:80"
      - "${NGINX_HOST_HTTPS_PORT:-443}:443"
    environment:
      - USER_ID=${USER_ID:-1000}
      - GROUP_ID=${GROUP_ID:-1000}
      - NGINX_HOST_HTTP_PORT=${NGINX_HOST_HTTP_PORT:-80}
      - NGINX_HOST_HTTPS_PORT=${NGINX_HOST_HTTPS_PORT:-443}
      - CSLANT_DOMAIN=${CSLANT_DOMAIN}
      - HOME_FE_PORT=${HOME_FE_PORT}
      - HOME_FE2_PORT=${HOME_FE2_PORT}
      - API_DOMAIN=${API_DOMAIN}
      - CSMS_DOMAIN=${CSMS_DOMAIN}
      - BLOG_FE_PORT=${BLOG_FE_PORT}
      - BLOG_API_DOMAIN=${BLOG_API_DOMAIN}
      - BLOG_API_ROUTE_PREFIX=${BLOG_API_ROUTE_PREFIX}
      - BLOG_ADMIN_DOMAIN=${BLOG_ADMIN_DOMAIN}
      - BLOG_ADMIN_DIR=${BLOG_ADMIN_DIR}
      - LARAVEL_BLOG_DOMAIN=${LARAVEL_BLOG_DOMAIN}
      - REPORTS_DOMAIN=${REPORTS_DOMAIN}
      - ASSETS_DOMAIN=${ASSETS_DOMAIN}
      - DOCS_DOMAIN=${DOCS_DOMAIN}
      - DOCS_PORT=${DOCS_PORT}
      - API_DOCS_DOMAIN=${API_DOCS_DOMAIN}
      - API_DOCS_PORT=${API_DOCS_PORT}
      - TZ=${TZ}

      ## Client domains
      - VWS_DOMAIN=${VWS_DOMAIN:-ent.vws.cslant.com.local}

      ## cslant.net domains
      - CSLANT_NET_DOMAIN=${CSLANT_NET_DOMAIN:-cslant.net.local}

      # Mustang backend domain and port
      - MUSTANG_API_DOMAIN=${MUSTANG_API_DOMAIN:-mustang.api.cslant.com.local}
      - MUSTANG_API_PORT=${MUSTANG_API_PORT:-3000}
    networks:
      - cslant_dev
    depends_on:
      - php83
      - php84
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 5

  ## BACKEND SERVICES (PHP) ====================================================
  php84:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-php84"
    image: cslant/dev-php84
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev
    environment:
      - TZ=${TZ}
    healthcheck:
      test: [ "CMD", "php", "-v" ]
      interval: 30s
      timeout: 10s
      retries: 5

  php83:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-php83"
    image: cslant/dev-php83
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev
    environment:
      - TZ=${TZ}
    healthcheck:
      test: [ "CMD", "php", "-v" ]
      interval: 30s
      timeout: 10s
      retries: 5

  php74:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-php74"
    image: cslant/dev-php74
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev
    environment:
      - TZ=${TZ}
    healthcheck:
      test: [ "CMD", "php", "-v" ]
      interval: 30s
      timeout: 10s
      retries: 5

  php56:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-php56"
    image: cslant/dev-php56
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev
    environment:
      - TZ=${TZ}
    healthcheck:
      test: [ "CMD", "php", "-v" ]
      interval: 30s
      timeout: 10s
      retries: 5

  ## WORKER SERVICES (PHP) =====================================================
  worker83:
    container_name: "${COMPOSE_PROJECT_NAME}-worker83"
    image: cslant/dev-worker83
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev
      - ./worker/php83/supervisor.d:/etc/supervisor.d

  worker84:
    container_name: "${COMPOSE_PROJECT_NAME}-worker84"
    image: cslant/dev-worker84
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev
      - ./worker/php84/supervisor.d:/etc/supervisor.d

  ## BACKEND SERVICES (NODE) =================================================
  mustang-backend:
    container_name: "${COMPOSE_PROJECT_NAME}-mustang-backend"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/mustang-backend:/var/dev/mustang-backend
    working_dir: "/var/dev/mustang-backend"
    command: [ "npx", "yarn", "${MUSTANG_COMMAND:-start:dev}" ]
    depends_on:
      - mysql9

  ## FRONTEND SERVICES (NODE) =================================================
  node22:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-node22"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}:/var/dev

  home-fe:
    container_name: "${COMPOSE_PROJECT_NAME}-home-fe"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/home-fe:/var/dev/home-fe
    working_dir: "/var/dev/home-fe"
    command: [ "npx", "yarn", "${HOME_FE_COMMAND:-start}", "--host", "0.0.0.0" ]

  home-fe2:
    container_name: "${COMPOSE_PROJECT_NAME}-home-fe2"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/home-fe2:/var/dev/home-fe2
    working_dir: "/var/dev/home-fe2"
    command: [ "npx", "yarn", "${HOME_FE2_COMMAND:-start}" ]

  blog-fe:
    container_name: "${COMPOSE_PROJECT_NAME}-blog-fe"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/blog-fe:/var/dev/blog-fe
    working_dir: "/var/dev/blog-fe"
    command: [ "npx", "yarn", "${BLOG_FE_COMMAND:-start}", "--host", "0.0.0.0" ]

  docs:
    container_name: "${COMPOSE_PROJECT_NAME}-docs"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/docs:/var/dev/docs
    working_dir: "/var/dev/docs"
    command: [ "npx", "yarn", "${DOCS_COMMAND:-serve}", "--host", "0.0.0.0" ]

  api-docs:
    container_name: "${COMPOSE_PROJECT_NAME}-api-docs"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/docs:/var/dev/api-docs
    working_dir: "/var/dev/api-docs"
    command: [ "npx", "yarn", "${API_DOCS_COMMAND:-serve}", "--host", "0.0.0.0" ]

  mfe_footer:
    container_name: "${COMPOSE_PROJECT_NAME}-mfe_footer"
    image: cslant/dev-node22
    networks:
      - cslant_dev
    volumes:
      - ${SOURCE_CODE_PATH}/mfe-footer:/var/dev/mfe-footer
    working_dir: "/var/dev/mfe-footer"
    command: [ "npx", "yarn", "${MFE_FOOTER_COMMAND:-serve}", "--host", "0.0.0.0" ]

  ## DATABASE SERVICES =========================================================
  mysql:
    container_name: "${COMPOSE_PROJECT_NAME}-mysql"
    image: cslant/dev-mysql
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-p${MYSQL_ROOT_PASSWORD}" ]
      interval: 10s
      timeout: 5s
      retries: 3
    environment:
      - "MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}"
      - "TZ=${TZ}"
    volumes:
      - mysql:/var/lib/mysql
      - ./mysql/sql:/var/lib/mysql-files
      - ./mysql/entry.d:/docker-entrypoint-initdb.d
    ports:
      - "${MYSQL_HOST_PORT}:3306"
    networks:
      - cslant_dev

  mysql57:
    container_name: "${COMPOSE_PROJECT_NAME}-mysql57"
    image: cslant/dev-mysql57
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-p${MYSQL_ROOT_PASSWORD}" ]
      interval: 10s
      timeout: 5s
      retries: 3
    environment:
      - "MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}"
      - "TZ=${TZ}"
    volumes:
      - mysql57:/var/lib/mysql
      - ./mysql/sql:/var/lib/mysql-files
      - ./mysql/entry.d:/docker-entrypoint-initdb.d
    ports:
      - "${MYSQL57_HOST_PORT}:3306"
    networks:
      - cslant_dev

  # MySQL 9 for Mustang Backend
  mysql9:
    container_name: "${COMPOSE_PROJECT_NAME}-mysql9"
    build:
      - context: docker-images/mysql9
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-p${MYSQL_ROOT_PASSWORD}" ]
      interval: 10s
      timeout: 5s
      retries: 3
    environment:
      - "MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}"
      - "TZ=${TZ}"
    volumes:
      - mysql9:/var/lib/mysql
      - ./mysql/sql:/var/lib/mysql-files
      - ./mysql/entry.d:/docker-entrypoint-initdb.d
    networks:
      - cslant_dev

  postgres:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-postgres"
    image: cslant/dev-postgres
    networks:
      - cslant_dev
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER}" ]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - postgres:/var/lib/postgresql/data
      - ./postgres/entry.d:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - "${POSTGRES_HOST_PORT:-5432}:5432"

  ## ELASTICSEARCH SERVICE =====================================================
  elasticsearch:
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-elasticsearch"
    build:
        context: elastic
        args:
          ELASTIC_STACK_VERSION: ${ELASTIC_STACK_VERSION:-8.14.3}
    environment:
      - discovery.type=${ELASTICSEARCH_DISCOVERY_TYPE:-single-node}
      - xpack.security.enabled=${ELASTICSEARCH_XPACK_SECURITY_ENABLED:-false}
      - ES_JAVA_OPTS=${ELASTICSEARCH_JAVA_OPTS:-"-Xms512m -Xmx512m"}
      - xpack.ml.enabled=${ELASTICSEARCH_XPACK_ML_ENABLED:-false}
      - bootstrap.memory_lock=true
    ports:
      - "${ELASTICSEARCH_HTTP_PORT:-9200}:9200"
      - "${ELASTICSEARCH_TRANSPORT_PORT:-9300}:9300"
    networks:
      - cslant_dev
    volumes:
      - elasticsearch:/var/lib/elasticsearch/data
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 5
    depends_on:
      - mysql
      - postgres

  ## MAIL HOG ==================================================================
  mailhog:
    image: cslant/dev-mailhog
    container_name: "${COMPOSE_PROJECT_NAME:-cslant}-mailhog"
    environment:
      - TZ=${TZ}
    ports:
      - "${MAILHOG_SMTP_PORT:-1025}:1025"
      - "${MAILHOG_WEB_UI_PORT:-8025}:8025"
    networks:
      - cslant_dev
