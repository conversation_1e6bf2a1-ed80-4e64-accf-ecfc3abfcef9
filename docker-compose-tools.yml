networks:
  cslant_dev:
    external: true

volumes:
  pgadmin:
    driver: local

services:
  ## DATABASE ADMINISTRATION
  pgadmin:
    container_name: "${COMPOSE_PROJECT_NAME:-blog}-pgadmin"
    image: dpage/pgadmin4
    networks:
      - cslant_dev
    ports:
      - "${PGADMIN_HOST_PORT:-5050}:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
      PGADMIN_SETUP_EMAIL: ${PGADMIN_DEFAULT_EMAIL}
      PGADMIN_SETUP_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
      PGADMIN_CONFIG_SERVER_MODE: 'True'
      PGADMIN_CONFIG_ENHANCED_COOKIE_PROTECTION: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
      PGADMIN_LISTEN_PORT: 80
    volumes:
      - pgadmin:/var/lib/pgadmin

  ## KIBANA SERVICE
  kibana:
    image: docker.elastic.co/kibana/kibana:${ELASTIC_STACK_VERSION:-8.14.3}
    container_name: "${COMPOSE_PROJECT_NAME}-kibana"
    platform: ${PLATFORM:-linux/amd64}
    environment:
      - node.name=kibana
      - cluster.name=elasticsearch-cluster
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=${KIBANA_PASSWORD:-changeme}
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    depends_on:
      - elasticsearch
    healthcheck:
      test: [ "CMD-SHELL", "curl -s -I http://localhost:5601 | grep -q 'HTTP/1.1 302 Found'" ]
      interval: 30s
      timeout: 5s
      retries: 3
    networks:
      - cslant_dev

  ## DATABASE TOOLS
  phpmyadmin:
    # image: phpmyadmin/phpmyadmin
    container_name: "${COMPOSE_PROJECT_NAME}-phpmyadmin"
    build:
      context: phpmyadmin
    platform: ${PLATFORM:-linux/amd64}

    restart: always
    ports:
      - "${PHPMYADMIN_PORT:-9024}:80"
    environment:
      PMA_HOST: mysql
      UPLOAD_LIMIT: 2048M
    networks:
      - cslant_dev
