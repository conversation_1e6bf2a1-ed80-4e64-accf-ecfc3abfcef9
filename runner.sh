#!/bin/bash

# sync env file
if [ ! -f .env ]; then
  if ! command -v envsubst &> /dev/null; then
    cp .env.example .env
  else
    envsubst < .env.example > .env
  fi
fi

set -a
# shellcheck disable=SC1091
source .env
set +a
set -ue

source ./setup/variables.sh
source ./setup/configs/import.sh

define_default_repos

case "${1:-*}" in
  welcome | w)
    welcome
    ;;

  help | h)
    usage
    ;;

  ssl)
    ssl
    ;;

  network | n)
    network
    ;;

  build | b)
    build
    ;;

  build_all | ba)
    build_all
    ;;

  blog_git_sync | bgs)
    # $2: all, admin, fe, api-package
    # $3: -f or --force
    blog_git_sync "$2" "${3:-none}"
    ;;

  home_git_sync | hgs)
    # $2: all, api, fe
    # $3: -f or --force
    home_git_sync "$2" "${3:-none}"
    ;;

  install | i)
    install
    ;;

  update | u)
    update
    ;;

  special_install | si)
    force_sync_accept
    special_install $2 "${3:-none}"
    ;;

  client_install | ci | client | ent)
    client_install $2 "${3:-none}" "${4:-none}"
    ;;

  resource | r)
    resource "${2:-none}"
    ;;

  start | s)
    start
    ;;

  start_all | sa)
    start_all
    ;;

  resource_database | rd)
    resource_database
    ;;

  es_import | ei)
    elasticsearch_import
    ;;

  all | a)
    force_sync_accept
    ssl
    build
    blog_git_sync all "${2:-none}"
    home_git_sync all "${2:-none}"
    resource "${2:-none}"
    install
    start
    ;;

  docker_sync_repo | dsr)
    docker_sync_repo
    ;;

  cslant.net)
    cslant_net_handler "${@:2}"
    ;;

  mustang_backend | mustang)
    # $2: -f or --force for force sync
    mustang_backend_sync "${2:-none}"
    ;;

  *)
    usage
    exit 1
    ;;
esac
