#!/bin/bash

comic_news_copy_docker_compose() {
  echo '📦 Copying Comic news docker compose files...'
  cd "$MAIN_CURRENT_DIR" || exit
  cp -rf $MAIN_CURRENT_DIR/clients/cl-tien-comic-news-scripts/docker-compose-comic-news.yml $MAIN_CURRENT_DIR/docker-compose-comic-news.yml
}

comic_news_docker_start() {
  docker compose -f docker-compose.yml -f docker-compose-comic-news.yml up -d nginx node22 php84 mysql mailhog comic-news
  reload_nginx_service
}

comic_news_platform_sync() {
  COMIC_NEW_REPO_NAME='cl-tien-comic-news-platform'

  echo '📥 Syncing Comic News Platform repo...'

  cd "$SOURCE_DIR" || exit

  git_sync_handle $COMIC_NEW_REPO_NAME "" $1

  cd "$SOURCE_DIR/$COMIC_NEW_REPO_NAME/backend" || exit
  git_force_sync "$1" "blog-private-modules"
  git_handler "blog-private-modules"

  cd "$SOURCE_DIR/$COMIC_NEW_REPO_NAME/backend/packages" || exit
  git_force_sync "$1" "blog-core"
  git_handler "blog-core"

  cd "$SOURCE_DIR/$COMIC_NEW_REPO_NAME/backend/packages" || exit
  git_force_sync "$1" "blog-api-package"
  git_handler "blog-api-package"

  echo ''
}

comic_news_copy_nginx_conf() {
  echo '📡 Copying nginx config...'
  cd "$MAIN_CURRENT_DIR" || exit
  cp -rf $MAIN_CURRENT_DIR/clients/cl-tien-comic-news-scripts/conf/* $MAIN_CURRENT_DIR/nginx/templates/
}

comic_news_docker_run() {
  comic_news_copy_docker_compose

  cd "$MAIN_CURRENT_DIR" || exit

  docker compose pull nginx node22 php84 mysql mailhog
  docker compose -f docker-compose-comic-news.yml pull
}

comic_news_install() {
    comic_news_docker_run
    comic_news_source_implement install
}

comic_news_source_implement() {
  if [ "$1" == "install" ]; then
    COMPOSER_COMMAND="install"
  else
    COMPOSER_COMMAND="update"
  fi

  echo "  ∟ Comic News backend..."
  env "$SOURCE_DIR/cl-tien-comic-news-platform/backend"
  docker compose run --rm -w /var/dev/cl-tien-comic-news-platform/backend php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  echo "  ∟ Comic News frontend..."
  env "$SOURCE_DIR/cl-tien-comic-news-platform/frontend"
  docker compose run --rm -w /var/dev/cl-tien-comic-news-platform/frontend node22 ash -l -c "\
      yarn install; \
  "
}

comic_news_handler() {
  case "$1" in
    "git-sync")
      echo '📦 Syncing Comic News...'
      comic_news_platform_sync "$2"
      comic_news_copy_nginx_conf
      ;;
    "first")
      echo 'Configuring Comic News...'
      create_cert_items 'outsrc.tien.news.cslant.com.local'
      comic_news_copy_docker_compose
      ;;
    "install" | "in")
      echo '📦 Installing Comic News...'
      comic_news_install
      ;;
    "start")
      echo '📦 Starting Comic News...'
      comic_news_copy_nginx_conf
      comic_news_copy_docker_compose
      comic_news_docker_start
      ;;
    "all")
      echo '📦 Syncing and installing Comic News...'
      comic_news_platform_sync "$2"
      comic_news_copy_nginx_conf
      create_cert_items 'outsrc.tien.news.cslant.com.local'
      comic_news_copy_docker_compose
      comic_news_docker_run
      comic_news_source_implement install
      comic_news_docker_start
      ;;
    *)
      echo "Invalid argument: $1"
      ;;
 esac
}
