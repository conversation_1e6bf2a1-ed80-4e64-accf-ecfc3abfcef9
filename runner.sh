#!/bin/bash

# sync env file
if [ ! -f .env ]; then
  if ! command -v envsubst &> /dev/null; then
    cp .env.example .env
  else
    envsubst < .env.example > .env
  fi
fi

set -a
# shellcheck disable=SC1091
source .env
set +a
set -ue

source ./scripts/variables.sh
source ./scripts/git-info.sh
source ./scripts/functions.sh

case ${1:-} in
  github | gh)
    sync_from_github $2 $3 ${4:-}
    ;;
  gitlab | gl)
    sync_from_gitlab $2 $3 ${4:-}
    ;;
  *)
    echo "Usage: $0 {github|gitlab} {all|package|module} {main|develop} [-f|--force]"
    exit 1
    ;;
esac
