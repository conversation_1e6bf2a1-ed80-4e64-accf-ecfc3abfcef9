{"demo-repository": "on-board/demo-repository", "docslant": "cslant/docslant", "dev-docker-images": "cslant/configs/dev-docker-images", "git-sync-config": "cslant/configs/git-sync-config", "blog-runner": "cslant/configs/blog-runner", "home-runner": "cslant/configs/home-runner", "docs-runner": "cslant/configs/docs-runner", "reports-runner": "cslant/configs/reports-runner", "n8n-docker-compose": "cslant/configs/n8n-docker-compose", "mustang-crawler": "cslant/mustang-crawler", "mustang-backend": "cslant/mustang-backend", "docs": "cslant/inhouse/docs/docs", "api-docs": "cslant/inhouse/docs/api-docs", "blog-core": "cslant/inhouse/blog/blog-core", "blog-admin": "cslant/inhouse/blog/blog-admin", "blog-api-package": "cslant/inhouse/blog/blog-api-package", "blog-private-modules": "cslant/inhouse/blog/blog-private-modules", "home-fe2": "cslant/inhouse/home/<USER>", "cslant.net-flant": "cslant/cslant.net/flant", "cslant.net_pikachu": "cslant/cslant.net/pikachu", "cslant.net_wp_cslant-suite": "cslant/cslant.net/wp/cslant-suite", "cslant.net_wp_cslant-themes": "cslant/cslant.net/wp/cslant-themes", "ignore-home-fe": "cslant/inhouse/home/<USER>", "ignore-ent-vnwebsubmit-networking-tool": "freelancers/vietnamwebsubmit/networking-tool", "ignore-ent-vnwebsubmit-networking-tool-docker": "freelancers/vietnamwebsubmit/networking-tool-docker"}