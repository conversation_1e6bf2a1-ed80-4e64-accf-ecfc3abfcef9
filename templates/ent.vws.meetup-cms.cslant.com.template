server {
    listen 80;
    listen [::]:80;

    server_name ent.vws.meetup-cms.cslant.com.local;

    root /var/dev/vietnamwebsubmit/vws-meetup-cms;
    index index.php index.html index.htm;

    access_log /var/log/clients/ent.vws.meetup-cms.cslant.com.local-access.log;
    error_log /var/log/clients/ent.vws.meetup-cms.cslant.com.local-error.log debug;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    set $upstream_backend56 php56:9000;

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass $upstream_backend56;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    location ~ /\.ht {
         deny all;
    }
}
