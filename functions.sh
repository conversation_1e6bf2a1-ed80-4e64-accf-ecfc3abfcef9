#!/bin/bash

copy_nginx_conf() {
  echo '📡 Copying nginx config...'
  cd "$MAIN_CURRENT_DIR" || exit
  cp -rf $MAIN_CURRENT_DIR/clients/ent-vnwebsubmit-scripts/templates/* $MAIN_CURRENT_DIR/nginx/templates/
  cp -rf $MAIN_CURRENT_DIR/clients/ent-vnwebsubmit-scripts/conf/* $MAIN_CURRENT_DIR/nginx/conf/customs/
}

vietnam_web_submit_handler() {
  case "$1" in
    "first" | "f")
      echo 'Configuring VietnamWebSummit...'

      copy_nginx_conf
      create_cert_items ${VWS_DOMAIN:-ent.vws.cslant.com.local}
      copy_docker_compose
      ;;
    "all" | "a")
      echo '📦 Installing VietnamWebSummit...'

      vietnam_web_submit_git_sync all "$2"
      copy_nginx_conf
      create_cert_items ${VWS_DOMAIN:-ent.vws.cslant.com.local}
      copy_docker_compose
      vietnam_web_submit_add_storages
      vietnam_web_submit_install
      vietnam_web_submit_docker_start
      ;;
    "git-sync" | "gs")
      echo '📦 Syncing VietnamWebSummit...'

      vietnam_web_submit_git_sync all "$2"
      ;;
    "storage" | "st")
      echo '📦 Installing VietnamWebSummit storages...'

      vietnam_web_submit_add_storages
      ;;
    "install" | "in")
      echo '📦 Installing VietnamWebSummit...'

      copy_nginx_conf
      vietnam_web_submit_install
      vietnam_web_submit_docker_start
      ;;
    "start" | "s")
      echo '📦 Starting VietnamWebSummit...'

      copy_nginx_conf
      vietnam_web_submit_docker_start
      ;;

    "import-db" | "id")
      echo '📦 Importing VietnamWebSummit databases...'

      vietnam_web_submit_import_db $2
      ;;

    "export-db" | "ed")
      echo '📦 Exporting VietnamWebSummit databases...'

      vietnam_web_submit_export_db $2
      ;;
    *)
      echo "Invalid argument: $1"
      ;;
  esac
}

vietnam_web_submit_add_storages() {
  echo '📦 Adding VietnamWebSummit storages...'

  YEARS=(2020 2021 2022 2023)

  for YEAR in "${YEARS[@]}"; do
    echo "    📦 Adding VietnamWebSummit storages for year $YEAR..."

    if [ ! -d "$SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/$YEAR" ]; then
      cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage/uploads/$YEAR" "$SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/"
    else
      echo "      ∟ Directory $SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/$YEAR already exists"
    fi
  done

  YEARS=(2019)

  for YEAR in "${YEARS[@]}"; do
    echo "    📦 Adding VietnamWebSummit storage 2 for year $YEAR..."

    if [ ! -d "$SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/$YEAR" ]; then
    cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage2/uploads/$YEAR" "$SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/"
    else
    echo "      ∟ Directory $SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/$YEAR already exists"
    fi
  done

  YEARS=(2024)

  for YEAR in "${YEARS[@]}"; do
    echo "    📦 Adding VietnamWebSummit storage 3 for year $YEAR..."

    if [ ! -d "$SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/$YEAR" ]; then
      cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage3/uploads/$YEAR" "$SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/"
    else
      echo "      ∟ Directory $SOURCE_DIR/vietnamwebsubmit/vws-homepage/wp-content/uploads/$YEAR already exists"
    fi
  done
}

copy_docker_compose() {
  echo '📦 Copying docker compose files...'
  cd "$MAIN_CURRENT_DIR" || exit
  cp -rf $MAIN_CURRENT_DIR/clients/ent-vnwebsubmit-scripts/docker-compose-vws.yml $MAIN_CURRENT_DIR/docker-compose-vws.yml
}

vietnam_web_submit_docker_run() {
  copy_docker_compose

  cd "$MAIN_CURRENT_DIR" || exit

  docker compose pull nginx node22 php56 php74 php84 mysql mailhog
  docker compose -f docker-compose-vws.yml pull
}

vietnam_web_submit_install() {
  vietnam_web_submit_docker_run

  echo '  📦 Installing VietnamWebSummit sources...'
  vietnam_web_submit_source_implement install
}

vietnam_web_submit_docker_start() {
  copy_docker_compose
  docker compose -f docker-compose.yml -f docker-compose-vws.yml up -d nginx node22 php56 php74 php84 mysql mailhog vws mysql57
  reload_nginx_service
}

vietnam_web_submit_source_implement() {
  if [ "$1" == "install" ]; then
    COMPOSER_COMMAND="install"
  else
    COMPOSER_COMMAND="update"
  fi

  local VWS_DIR="$SOURCE_DIR/vietnamwebsubmit"

  echo "  ∟ Networking Tool..."
  env "$VWS_DIR/vws-networking-tool"
  docker compose run --rm -w /var/dev/vietnamwebsubmit/vws-networking-tool php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  docker compose run --rm -w /var/dev/vietnamwebsubmit/vws-networking-tool node22 ash -l -c "\
    yarn install; \
  "

  echo "  ∟ Meetup..."
  env "$VWS_DIR/vws-meetup"
  docker compose run --rm -w /var/dev/vietnamwebsubmit/vws-meetup php74 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  echo "  ∟ Meetup CMS..."
  envsubst < "$VWS_DIR/vws-meetup-cms/env.sample.php" > "$VWS_DIR/vws-meetup-cms/env.php"
  docker compose run --rm -w /var/dev/vietnamwebsubmit/vws-meetup-cms php56 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  echo "  ∟ VWS..."
  if [ ! -f "$VWS_DIR/vws-homepage/wp-config.php" ]; then
    cp -rf "$VWS_DIR/vws-homepage/wp-config-sample.php" "$VWS_DIR/vws-homepage/wp-config.php"
  fi
}

vietnam_web_submit_import_db() {
  vietnam_web_submit_docker_start
  case "$1" in
    "all")
      echo '  ∟  📦 Importing all databases...'

      vietnam_web_submit_import_networking_tool_db
      vietnam_web_submit_import_meetup_tickets_db
      vietnam_web_submit_import_wordpress_db
      vietnam_web_submit_import_meetup_manage_db
      vietnam_web_submit_import_course_topdev_db
      ;;
    "networking-tool")
      echo '  ∟  📦 Importing networking tool database...'

      vietnam_web_submit_import_networking_tool_db
      ;;
    "wordpress")
      echo '  ∟  📦 Importing wordpress homepage database...'

      vietnam_web_submit_import_wordpress_db
      ;;
    "meetup-tickets")
      echo '  ∟  📦 Importing meetup tickets database...'

      vietnam_web_submit_import_meetup_tickets_db
      ;;
    "meetup-manage")
      echo '  ∟  📦 Importing meetup manage database...'

      vietnam_web_submit_import_meetup_manage_db
      ;;
    "course-topdev")
        echo '  ∟  📦 Importing course_topdev database...'

        vietnam_web_submit_import_course_topdev_db
        ;;
    *)
      echo "Invalid argument: $1"
      ;;
  esac
}

vietnam_web_submit_export_db() {
  case "$1" in
    "networking-tool")
      echo '  ∟  📦 Exporting networking tool database...'

      vietnam_web_submit_export_networking_tool_db
      ;;
    "wordpress")
      echo '  ∟  📦 Exporting wordpress homepage database...'

      vietnam_web_submit_export_wordpress_db
      ;;
    "wordpress-to-storage2")
      echo '  ∟  📦 Exporting wordpress database to storage2...'

      vietnam_web_submit_export_wordpress_db_to_storage2
      ;;
    "meetup-tickets")
      echo '  ∟  📦 Exporting meetup tickets database...'

      vietnam_web_submit_export_meetup_tickets_db
      ;;
    "course-topdev")
      echo '  ∟  📦 Exporting course_topdev database...'

      vietnam_web_submit_export_course_topdev_db
      ;;
    *)
      echo "Invalid argument: $1"
      ;;
  esac
}

vietnam_web_submit_import_wordpress_db() {
  if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws-homepage.sql" ]; then
    cd "$SOURCE_DIR/vietnamwebsubmit/vws-storage2/databases" || exit

    cat vws_wp* > vws_wp.zip
    unzip vws_wp.zip -d "$MAIN_CURRENT_DIR/mysql/sql/"
    rm -rf vws_wp.zip

    if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws-homepage.sql" ]; then
      mv "$MAIN_CURRENT_DIR/mysql/sql/vws-homepage-2025-04-07.sql" "$MAIN_CURRENT_DIR/mysql/sql/vws-homepage.sql"
    fi
  fi

  cd "$MAIN_CURRENT_DIR" || exit
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    mysql -h mysql -u root -p${MYSQL_ROOT_PASSWORD} -e 'CREATE DATABASE vws_wp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; GRANT ALL PRIVILEGES ON vws_wp.* TO "root"@"mysql"; FLUSH PRIVILEGES;'; \
    mysql -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_wp < /var/lib/mysql-files/vws-homepage.sql; \
  "
}

vietnam_web_submit_export_wordpress_db() {
  cd "$MAIN_CURRENT_DIR" || exit
  DATE=$(date +%Y-%m-%d)
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysqldump -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_wp > /var/lib/mysql-files/vws-homepage-${DATE}.sql; \
  "
}

vietnam_web_submit_export_wordpress_db_to_storage2() {
  echo '  ∟  📦 Exporting wordpress database and splitting for storage2...'

  cd "$MAIN_CURRENT_DIR" || exit
  DATE=$(date +%Y-%m-%d)

  # Export database to SQL file
  echo '    ∟ Exporting database...'
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysqldump -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_wp > /var/lib/mysql-files/vws-homepage-${DATE}.sql; \
  "

  # Check if export was successful
  if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws-homepage-${DATE}.sql" ]; then
    echo "    ❌ Database export failed!"
    return 1
  fi

  echo '    ∟ Splitting SQL file into chunks...'

  # Create temporary directory for split files
  TEMP_DIR="$MAIN_CURRENT_DIR/mysql/sql/temp_split_${DATE}"
  mkdir -p "$TEMP_DIR"

  # Split the SQL file into 50MB chunks with prefix 'vws_wp.'
  split -b 50m "$MAIN_CURRENT_DIR/mysql/sql/vws-homepage-${DATE}.sql" "$TEMP_DIR/vws_wp."

  # Check if storage2 directory exists
  STORAGE2_DB_DIR="$SOURCE_DIR/vietnamwebsubmit/vws-storage2/databases"
  if [ ! -d "$STORAGE2_DB_DIR" ]; then
    echo "    ❌ Storage2 databases directory not found: $STORAGE2_DB_DIR"
    rm -rf "$TEMP_DIR"
    return 1
  fi

  echo '    ∟ Copying split files to storage2...'

  # Remove old split files from storage2
  rm -f "$STORAGE2_DB_DIR"/vws_wp.*

  # Copy new split files to storage2
  cp "$TEMP_DIR"/vws_wp.* "$STORAGE2_DB_DIR/"

  # Verify files were copied
  SPLIT_COUNT=$(ls -1 "$TEMP_DIR"/vws_wp.* 2>/dev/null | wc -l)
  COPIED_COUNT=$(ls -1 "$STORAGE2_DB_DIR"/vws_wp.* 2>/dev/null | wc -l)

  if [ "$SPLIT_COUNT" -eq "$COPIED_COUNT" ] && [ "$SPLIT_COUNT" -gt 0 ]; then
    echo "    ✅ Successfully copied $COPIED_COUNT split files to storage2"

    # List the created files
    echo "    ∟ Created files:"
    ls -lh "$STORAGE2_DB_DIR"/vws_wp.*
  else
    echo "    ❌ Error copying files. Expected: $SPLIT_COUNT, Copied: $COPIED_COUNT"
    rm -rf "$TEMP_DIR"
    return 1
  fi

  # Clean up temporary directory
  rm -rf "$TEMP_DIR"

  echo "    ✅ WordPress database exported and split successfully!"
  echo "    ∟ Original file: $MAIN_CURRENT_DIR/mysql/sql/vws-homepage-${DATE}.sql"
  echo "    ∟ Split files location: $STORAGE2_DB_DIR"
}

vietnam_web_submit_import_networking_tool_db() {
  if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws_networking_tool.sql" ]; then
    cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage/databases/qr_networking_2025-03-29.sql" "$MAIN_CURRENT_DIR/mysql/sql/vws_networking_tool.sql"
  fi

  cd "$MAIN_CURRENT_DIR" || exit
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysql -h mysql -u root -p${MYSQL_ROOT_PASSWORD} -e 'CREATE DATABASE vws_networking_tool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; GRANT ALL PRIVILEGES ON vws_networking_tool.* TO "root"@"mysql"; FLUSH PRIVILEGES;'; \
    mysql -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_networking_tool < /var/lib/mysql-files/vws_networking_tool.sql; \
  "
}

vietnam_web_submit_export_networking_tool_db() {
  cd "$MAIN_CURRENT_DIR" || exit
  DATE=$(date +%Y-%m-%d)
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysqldump -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_networking_tool > /var/lib/mysql-files/vws_networking_tool-${DATE}.sql; \
  "
}

vietnam_web_submit_import_meetup_manage_db() {
    if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_manage.sql" ]; then
        cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage/databases/meetup_manage_2025-05-10.sql" "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_manage.sql"
    fi

    cd "$MAIN_CURRENT_DIR" || exit
    docker compose run --rm -w /var/lib/mysql-files mysql57 bash -l -c "\
      export MYSQL_PWD=root; \
      mysql -h mysql57 -u root -p${MYSQL_ROOT_PASSWORD} -e 'CREATE DATABASE vws_meetup_manage CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; GRANT ALL PRIVILEGES ON vws_meetup_manage.* TO "root"@"mysql57"; FLUSH PRIVILEGES;'; \
      mysql -h mysql57 -u root -p${MYSQL_ROOT_PASSWORD} vws_meetup_manage < /var/lib/mysql-files/vws_meetup_manage.sql; \
    "
}

vietnam_web_submit_export_meetup_manage_db() {
    cd "$MAIN_CURRENT_DIR" || exit
    DATE=$(date +%Y-%m-%d)
    docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
      export MYSQL_PWD=root; \
      mysqldump -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_meetup_manage > /var/lib/mysql-files/vws_meetup_manage-${DATE}.sql; \
    "
}

vietnam_web_submit_import_meetup_tickets_db() {
  if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_tickets.sql" ]; then
    cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage/databases/meetup_tickets_2025-03-29.sql.zip" "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_tickets.sql.zip"

    unzip "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_tickets.sql.zip" -d "$MAIN_CURRENT_DIR/mysql/sql/"

    if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_tickets.sql" ]; then
      mv "$MAIN_CURRENT_DIR/mysql/sql/meetup_tickets_2025-03-29.sql" "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_tickets.sql"
    fi

    rm -rf "$MAIN_CURRENT_DIR/mysql/sql/vws_meetup_tickets.sql.zip"
  fi

  cd "$MAIN_CURRENT_DIR" || exit
  docker compose run --rm -w /var/lib/mysql-files mysql57 bash -l -c "\
    export MYSQL_PWD=root; \
    mysql -h mysql57 -u root -p${MYSQL_ROOT_PASSWORD} -e 'CREATE DATABASE vws_meetup_tickets CHARACTER SET utf8 COLLATE utf8_general_ci; GRANT ALL PRIVILEGES ON vws_meetup_tickets.* TO "root"@"mysql57"; FLUSH PRIVILEGES;'; \
    mysql -h mysql57 -u root -p${MYSQL_ROOT_PASSWORD} vws_meetup_tickets < /var/lib/mysql-files/vws_meetup_tickets.sql; \
  "
}

vietnam_web_submit_export_meetup_tickets_db() {
  cd "$MAIN_CURRENT_DIR" || exit
  DATE=$(date +%Y-%m-%d)
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysqldump -h mysql -u root -p${MYSQL_ROOT_PASSWORD} vws_meetup_tickets > /var/lib/mysql-files/vws_meetup_tickets-${DATE}.sql; \
  "
}

vietnam_web_submit_import_course_topdev_db() {
  if [ ! -f "$MAIN_CURRENT_DIR/mysql/sql/course_topdev_2025-07-09.sql" ]; then
    cp -rf "$SOURCE_DIR/vietnamwebsubmit/vws-storage/databases/course_topdev_2025-07-09.sql" "$MAIN_CURRENT_DIR/mysql/sql/"
  fi

  cd "$MAIN_CURRENT_DIR" || exit
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysql -h mysql57 -u root -p${MYSQL_ROOT_PASSWORD} -e 'CREATE DATABASE IF NOT EXISTS course_topdev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; GRANT ALL PRIVILEGES ON course_topdev.* TO "root"@"mysql"; FLUSH PRIVILEGES;'; \
    mysql -h mysql57 -u root -p${MYSQL_ROOT_PASSWORD} course_topdev < /var/lib/mysql-files/course_topdev_2025-07-09.sql; \
  "
}

vietnam_web_submit_export_course_topdev_db() {
  cd "$MAIN_CURRENT_DIR" || exit
  DATE=$(date +%Y-%m-%d)
  docker compose run --rm -w /var/lib/mysql-files mysql bash -l -c "\
    export MYSQL_PWD=root; \
    mysqldump -h mysql -u root -p${MYSQL_ROOT_PASSWORD} course_topdev > /var/lib/mysql-files/course_topdev-${DATE}.sql; \
  "
}

