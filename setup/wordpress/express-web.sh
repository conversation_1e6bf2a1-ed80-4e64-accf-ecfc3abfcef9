#!/bin/bash

# Main function to handle commands
express_web() {
  case "$1" in
    create)
      shift
      if [ ! -d "$SOURCE_DIR/wp-cslant-suite" ]; then
        cslant_net_sync "wp-suite" "no-force"
      fi
      if [ ! -d "$SOURCE_DIR/wp-cslant-themes/cslant-base" ]; then
        cslant_net_sync "wp-themes" "no-force"
      fi
      if [ ! -d "$SOURCE_DIR/cslant.net-wp-core" ]; then
        cslant_net_sync "wp-core" "no-force"
      fi
      if [ ! -d "$SOURCE_DIR/cslant.net-wp-core" ]; then
        echo -e "${RED}Error: cslant.net-wp-core directory not found!${NC}"
        exit 1
      fi
      if [ ! -d "$SOURCE_CODE_PATH/wordpress" ]; then
        mkdir -p "$SOURCE_CODE_PATH/wordpress"
      fi

      #  Ensure WP-CLI is installed
      install_wp_cli

      express_web_create "$@"
      ;;
    list)
      express_web_list
      ;;
    *)
      echo -e "${YELLOW}Usage: $0 cslant.net express-web {create|list} [slug]${NC}"
      echo -e "  create <slug>  Create a new WordPress site with the given slug"
      echo -e "  list           List all available WordPress sites"
      ;;
  esac
}

# Function to create a new WordPress site
express_web_create() {
    ##############################################
    ############## Helper Functions ##############
    ##############################################
    local slug="$1"
    local site_dir="${SOURCE_CODE_PATH}/wordpress/${slug}"
    local db_name=wp-$(echo -n "$slug" | sha1sum | cut -c1-8)
    local db_user="root"
    local db_password=${MYSQL_ROOT_PASSWORD}
    local db_host="mysql"
    local url="${slug}.demo.cslant.net.local"
    local real_url="${url}"

    # Add port if not is 80
    if [ "${NGINX_HOST_HTTP_PORT}" != "80" ]; then
        real_url="${url}:${NGINX_HOST_HTTP_PORT}"
    fi

    local admin_user=admin_$(echo -n "$slug" | sha1sum | cut -c1-8)
    local admin_password=$(openssl rand -base64 16 | tr -dc 'a-zA-Z0-9' | head -c 16)
    local admin_email="admin@$url"

    # local wp_cli="docker-compose -f ${MAIN_CURRENT_DIR}/docker-compose.yml exec -T wordpress wp"
    local wp_cli="wp"

    # Cleanup function to remove old site dir and database
    express_web_cleanup_site() {
        echo -e "\n${YELLOW}Cleaning up old site directory and database (if any)...${NC}"
        # Remove site directory if exists
        if [ -d "${site_dir}" ]; then
            rm -rf "${site_dir}"
            echo -e "${CYAN}Removed old site directory: ${site_dir}${NC}"
        fi
        # Drop database if exists
        cd "${MAIN_CURRENT_DIR}" || exit
        docker_start_single_service mysql
        while [[ $(docker compose ps | grep "$COMPOSE_PROJECT_NAME-mysql" | grep -c "healthy") != 1 ]]; do
            sleep 1
        done
        docker compose run --rm -w /var/lib/mysql mysql bash -l -c "\
            export MYSQL_PWD=root; \
            mysql -h mysql -u root -p${MYSQL_ROOT_PASSWORD} -e 'DROP DATABASE IF EXISTS \`${db_name}\`;'\
        "
        echo -e "${CYAN}Dropped old database ${BLUE}${db_name}${CYAN} (if it existed)${NC}"
    }

    echo -e "\n${YELLOW}Creating new WordPress site: ${slug}${NC}"
    echo -e "${CYAN}Site Directory: ${site_dir}${NC}"
    echo -e "${CYAN}Database Name: ${db_name}${NC}"

    express_web_create_wp_content_directories() {
        # Ensure wp-content directory exists
        mkdir -p "${site_dir}/wp-content"

        # Set proper permissions
        chmod 755 "${site_dir}"
        find "${site_dir}" -type d -exec chmod 755 {} \;
        find "${site_dir}" -type f -exec chmod 644 {} \;

        # Create site-specific directories with proper structure
        echo -e "\n${YELLOW}Creating site-specific directories...${NC}"
        local wp_dirs=(
            "${site_dir}/wp-content"
            "${site_dir}/wp-content/plugins"
            "${site_dir}/wp-content/themes"
            "${site_dir}/wp-content/uploads"
            "${site_dir}/wp-content/mu-plugins"
        )

        for dir in "${wp_dirs[@]}"; do
            mkdir -p "$dir"
            chmod 755 "$dir"
        done

        # Set ownership if needed (uncomment and adjust if running as root)
        # chown -R www-data:www-data "${site_dir}"
    }

    express_web_symlinks() {
        echo -e "\n${YELLOW}Linking WordPress core files...${NC}"

        # Create necessary symlinks
        ln -sfn "${SOURCE_CODE_PATH}/cslant.net-wp-core/wp-admin" "${site_dir}/wp-admin"
        ln -sfn "${SOURCE_CODE_PATH}/cslant.net-wp-core/wp-includes" "${site_dir}/wp-includes"

        # Symlink PHP files individually to avoid recursive symlinks
        for file in "${SOURCE_CODE_PATH}"/cslant.net-wp-core/*.php; do
            if [ -f "$file" ]; then
                ln -sfn "$file" "${site_dir}/$(basename "$file")"
            fi
        done

        express_web_create_wp_content_directories

        # Link shared plugins and themes
        echo -e "\n${YELLOW}Linking shared plugins and themes...${NC}"

        # Symlink individual plugins from cslant.net-wp-core
        if [ -d "${SOURCE_CODE_PATH}/cslant.net-wp-core/wp-content/plugins" ]; then
            echo -e "${YELLOW}Linking core plugins...${NC}"
            for plugin in "${SOURCE_CODE_PATH}"/cslant.net-wp-core/wp-content/plugins/*; do
                if [ -d "$plugin" ]; then
                    local plugin_name=$(basename "$plugin")
                    echo -e "  - Linking plugin: ${plugin_name}"
                    ln -sfn "$plugin" "${site_dir}/wp-content/plugins/${plugin_name}" || {
                        echo -e "${RED}Failed to link plugin: ${plugin_name}${NC}"
                        continue
                    }
                fi
            done
        fi

        # Link cslant-suite plugin if exists
        if [ -d "${SOURCE_CODE_PATH}/wp-cslant-suite" ]; then
            echo -e "\n${YELLOW}Linking cslant-suite...${NC}"
            cslant_net_wp_symlink "plugin" "cslant-suite" "${site_dir}" || {
                echo -e "${YELLOW}Warning: Failed to link cslant-suite plugin${NC}"
            }
        fi

        # Symlink themes
        if [ -d "${SOURCE_CODE_PATH}/wp-cslant-themes" ]; then
            mkdir -p "${site_dir}/wp-content/themes"
            for theme in "${SOURCE_CODE_PATH}"/wp-cslant-themes/*; do
                if [ -d "$theme" ]; then
                    local theme_name=$(basename "$theme")
                    ln -sfn "$theme" "${site_dir}/wp-content/themes/${theme_name}"
                fi
            done
        fi
    }

    express_web_create_wp_config_wp_cli() {
        # Get IP of mysql container
        cd "${MAIN_CURRENT_DIR}" || exit
        local mysql_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${COMPOSE_PROJECT_NAME}-mysql)
        echo -e "\n${CYAN}MySQL IP: ${mysql_ip}${NC}"

        if [ -f "${site_dir}/wp-config.php" ]; then
            rm "${site_dir}/wp-config.php"
        fi
        echo -e "\n${YELLOW}Creating wp-config.php...${NC}"
        ${wp_cli} config create --path="${site_dir}" \
            --dbname="${db_name}" \
            --dbuser="${db_user}" \
            --dbpass="${db_password}" \
            --dbhost="${mysql_ip}" \
            --dbprefix="wp_" \
            --skip-check \
            --force

        echo -e "\n${GREEN}✅  WordPress site '${slug}' created configured successfully!${NC}"

        # Install WordPress core with WP-CLI
        echo -e "\n${YELLOW}Installing WordPress...${NC}"
        ${wp_cli} core install --path="${site_dir}" \
            --url="http://${real_url}" \
            --title="${slug}" \
            --admin_user="${admin_user}" \
            --admin_password="${admin_password}" \
            --admin_email="${admin_email}" \
            --skip-email || {
            echo -e "${YELLOW}WordPress might already be installed, continuing...${NC}"
        }
        echo -e "  "

        chmod 755 "${site_dir}" || true
        chmod 644 "${site_dir}"/*.php 2>/dev/null || true

        echo -e "${GREEN}WordPress site setup completed!${NC}\n"

        # Activate required plugins and theme
        cd "${site_dir}" || exit
        echo -e "${CYAN}Activating theme and plugins...${NC}"
        ${wp_cli} theme activate cslant-base --path="${site_dir}" || true
        ${wp_cli} plugin activate wordpress-seo --path="${site_dir}" || true
        ${wp_cli} plugin activate woocommerce --path="${site_dir}" || true
        ${wp_cli} plugin activate contact-form-7 --path="${site_dir}" || true
        ${wp_cli} plugin activate cslant-suite --path="${site_dir}" || true
        echo -e "${GREEN}✓ Plugins and themes activated successfully!${NC}"

        ${wp_cli} user <NAME_EMAIL> \
            --role=cslant_customer \
            --user_pass="demo@cslant" \
            --path="${site_dir}" || true
        echo -e "\n${GREEN}Demo users created successfully!${NC}"
    }

    express_web_reset_symlinks_nginx() {
        echo -e "\n${YELLOW}Setting up symlinks for WordPress core files...${NC}"
        
        # Backup the site's wp-config.php if it exists
        if [ -f "${site_dir}/wp-config.php" ]; then
            cp "${site_dir}/wp-config.php" "${site_dir}/.wp-config.php.bak"
        fi
        
        # Clean up the site directory except our backup
        find "${site_dir}" -mindepth 1 ! -name '.wp-config.php.bak' -exec rm -rf {} +

        echo -e "${CYAN}Linking WordPress core files...${NC}"
        cd "${MAIN_CURRENT_DIR}" || exit
        
        # First, create all necessary directories
        docker compose run --rm -w "${site_dir}" nginx ash -l -c "\
            mkdir -p /var/dev/wordpress/${slug}/wp-content; \
            mkdir -p /var/dev/wordpress/${slug}/wp-content/plugins; \
            mkdir -p /var/dev/wordpress/${slug}/wp-content/themes; \
            mkdir -p /var/dev/wordpress/${slug}/wp-content/uploads; \
            mkdir -p /var/dev/wordpress/${slug}/wp-content/mu-plugins; \
        "
        
        # Link core directories
        docker compose run --rm -w "${site_dir}" nginx ash -l -c "\
            ln -sfn /var/dev/cslant.net-wp-core/wp-admin /var/dev/wordpress/${slug}/wp-admin; \
            ln -sfn /var/dev/cslant.net-wp-core/wp-includes /var/dev/wordpress/${slug}/wp-includes; \
        "
        
        # Link PHP files but exclude wp-config.php
        docker compose run --rm -w "${site_dir}" nginx ash -l -c "\
            for file in /var/dev/cslant.net-wp-core/*.php; do \
                if [ \"\$(basename \"\$file\")\" != \"wp-config.php\" ]; then \
                    ln -sfn \"\$file\" /var/dev/wordpress/${slug}/; \
                fi; \
            done; \
        "
        
        # Link plugins and themes
        docker compose run --rm -w "${site_dir}" nginx ash -l -c "\
            ln -sfn /var/dev/cslant.net-wp-core/wp-content/plugins/* /var/dev/wordpress/${slug}/wp-content/plugins/; \
            ln -sfn /var/dev/wp-cslant-suite /var/dev/wordpress/${slug}/wp-content/plugins/cslant-suite; \
            ln -sfn /var/dev/wp-cslant-themes/* /var/dev/wordpress/${slug}/wp-content/themes/; \
        "
        
        # Restore or create wp-config.php
        if [ -f "${site_dir}/.wp-config.php.bak" ]; then
            # Restore the original wp-config.php
            mv "${site_dir}/.wp-config.php.bak" "${site_dir}/wp-config.php"
            echo -e "${GREEN}Restored original wp-config.php${NC}"
        else
            # Create a new wp-config.php if none existed
            docker compose run --rm -w "${site_dir}" nginx ash -l -c "\
                cp /var/dev/cslant.net-wp-core/wp-config-sample.php /var/dev/wordpress/${slug}/wp-config.php; \
                sed -i \"s/define( *'DB_HOST', *'[^']*' */define('DB_HOST', 'mysql'/g\" /var/dev/wordpress/${slug}/wp-config.php; \
                # Set database credentials
                sed -i \"s/database_name_here/${db_name}/g\" /var/dev/wordpress/${slug}/wp-config.php; \
                sed -i \"s/username_here/root/g\" /var/dev/wordpress/${slug}/wp-config.php; \
                sed -i \"s/password_here/root/g\" /var/dev/wordpress/${slug}/wp-config.php; \
            "
            echo -e "${YELLOW}Created new wp-config.php with database settings${NC}"
        fi
        
        # Ensure wp-config.php is not a symlink
        if [ -L "${site_dir}/wp-config.php" ]; then
            local temp_conf=$(mktemp)
            cp -f "${site_dir}/wp-config.php" "$temp_conf"
            rm -f "${site_dir}/wp-config.php"
            mv "$temp_conf" "${site_dir}/wp-config.php"
        fi
        
        # Set proper permissions
        chmod 644 "${site_dir}/wp-config.php"
        
        # Add a check to prevent loading wp-config.php from parent directories

#         # Use a different delimiter (#) to avoid issues with slashes in paths
#         sed -i.bak "/define( *'ABSPATH',/d" "${site_dir}/wp-config.php"
#         # Add ABSPATH definition after the opening PHP tag
#         sed -i.bak $'/^<?php/a\
# define( \'ABSPATH\', \$_SERVER[\'DOCUMENT_ROOT\'] . \'/\' );' "${site_dir}/wp-config.php"
#         # Clean up backup file
#         rm -f "${site_dir}/wp-config.php.bak"
        
        echo -e "${GREEN}Symlinks and wp-config.php set up successfully!${NC}"
    }
    ############## END Helper Functions ##############












    ##################################################
    ############## Main Script Execution #############
    ##################################################

    # Call cleanup before proceeding
    express_web_cleanup_site

    ######
    ### Check if wildcard template exists
    ######
    local nginx_wildcard_template="${MAIN_CURRENT_DIR}/nginx/templates/wildcard-wp-express-web.demo.cslant.net.template"

    if [ ! -f "${nginx_wildcard_template}" ]; then
        echo -e "${RED}Error: Nginx wildcard template not found at ${nginx_wildcard_template}${NC}"
        return 1
    fi

    ######
    ### Create site directory
    ######
    if [ -d "${site_dir}" ] && [ "$(ls -A "${site_dir}")" ]; then # Check if the site directory already exists and is empty
        echo -e "${RED}Error: Directory ${site_dir} already exists${NC}"
        return 1
    fi
    mkdir -p "${site_dir}"

    ######
    ### Create database
    ######
    echo -e "\n${YELLOW}Creating database...${NC}"

    docker_start_single_service mysql

    echo -e "\n${CYAN}◎ Creating database '${db_name}' with user '${db_user}'...${NC}"
    cd "${MAIN_CURRENT_DIR}" || exit
    while [[ $(docker compose ps | grep "$COMPOSE_PROJECT_NAME-mysql" | grep -c "healthy") != 1 ]]; do
        sleep 1
    done
    # Create a temporary SQL file
    SQL_TEMP_FILE=$(mktemp /tmp/mysql_cmds.XXXXXX)
    cat > "$SQL_TEMP_FILE" << 'EOSQL'
CREATE DATABASE IF NOT EXISTS `%DB_NAME%` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

GRANT ALL PRIVILEGES ON `%DB_NAME%`.* TO 'root'@'%';
FLUSH PRIVILEGES;
EOSQL

    # Replace the placeholder with the actual database name
    if [[ "$(uname)" == "Darwin" ]]; then
        # macOS requires a backup extension with -i
        sed -i '' "s/%DB_NAME%/${db_name}/g" "$SQL_TEMP_FILE"
    else
        # Linux doesn't need backup extension
        sed -i "s/%DB_NAME%/${db_name}/g" "$SQL_TEMP_FILE"
    fi

    # Execute the SQL file with proper cleanup
    if ! docker compose run --rm -T mysql mysql -h mysql -u root -p"${MYSQL_ROOT_PASSWORD}" < "$SQL_TEMP_FILE"; then
        echo -e "${RED}❌ Error executing MySQL commands${NC}"
        rm -f "$SQL_TEMP_FILE"
        return 1
    fi

    # Clean up
    rm -f "$SQL_TEMP_FILE"

    # Verify the database was created
    if ! docker compose run --rm -T mysql mysql -h mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "USE \`${db_name}\`;" 2>/dev/null; then
        echo -e "${RED}❌ Failed to create database ${db_name}${NC}"
        return 1
    fi

    ######
    ### Link WordPress core files (Symlink)
    ######
    express_web_symlinks || {
        echo -e "${RED}❌ Error linking WordPress core files. Please check the setup.${NC}"
        return 1
    }
    
    echo -e "\n${GREEN}Using wildcard Nginx template for *.demo.cslant.net.local${NC}"

    ######
    ### Reload Nginx to apply changes
    ######
    reload_nginx_service

    ######
    ### Add to hosts file
    ######
    echo ''
    echo -e "\n${YELLOW}Adding ${BLUE}${url}${NC} to /etc/hosts file...${NC}"
    if ! grep -q "${url}" /etc/hosts; then
        echo "127.0.0.1 ${url}" | sudo tee -a /etc/hosts
    fi

    ######
    ### Create wp-config.php using WP-CLI
    ######
    express_web_create_wp_config_wp_cli || {
        echo -e "${RED}❌ Error creating wp-config.php or installing WordPress. Please check the setup.${NC}"
        return 1
    }

    ######
    ### Reset up symlinks for WordPress core files using with nginx container
    ######
    express_web_reset_symlinks_nginx

    # Write summary to a file and print to console
    summary_file="${site_dir}/site-summary.txt"
    summary_content="$(cat <<EOF
=== WordPress Deployment Summary ===
Domain:           http://${real_url}
Admin URL:        http://${real_url}/wp-admin/
Installation dir: ${site_dir}

Database Host:    ${db_host}
Database Name:    ${db_name}
Database User:    ${db_user}
Database Pass:    ${db_password}

Admin User:       ${admin_user}
Admin Password:   ${admin_password}
-
Demo User:        demo
Demo Password:    demo@cslant
EOF
)"
    echo "$summary_content" > "$summary_file"

    ### Output summary
    echo "=== WordPress Deployment Summary ==="
    echo -e "${CYAN}Domain:           http://${real_url}${NC}"
    echo -e "${CYAN}Admin URL:        http://${real_url}/wp-admin/${NC}"
    echo -e "${CYAN}Installation dir: ${site_dir}${NC}"
    echo ''
    echo -e "${CYAN}Database Host:    ${db_host}${NC}"
    echo -e "${CYAN}Database Name:    ${db_name}${NC}"
    echo -e "${CYAN}Database User:    ${db_user}${NC}"
    echo -e "${CYAN}Database Pass:    ${db_password}${NC}"
    echo ''
    echo -e "${CYAN}Admin User:       ${admin_user}${NC}"
    echo -e "${CYAN}Admin Password:   ${admin_password}${NC}"
    echo -e "${CYAN}-${NC}"
    echo -e "${CYAN}WordPress Demo:   demo${NC}"
    echo -e "${CYAN}Demo Password:    demo@cslant${NC}"
    echo ''
    echo -e "${GREEN}Summary written to ${summary_file}${NC}"
    echo -e "${GREEN}Done!!!${NC}"
}

# Function to list all WordPress sites
express_web_list() {
    echo -e "${YELLOW}Checking for existing WordPress sites...${NC}"
    if [ -d "${SOURCE_CODE_PATH}/wordpress" ]; then
        find "${SOURCE_CODE_PATH}/wordpress" -maxdepth 1 -type d ! -name "wordpress" -exec basename {} \; | sort
    else
        echo -e "${RED}No WordPress sites found.${NC}"
    fi
}

# WP plugin/theme Symlink functions
cslant_net_wp_symlink() {
  TYPE=${1:-'plugin'}
  WP_OBJECT_NAME=${2:-'cslant-base'}
  WP_SITE_ABSOLUTE_PATH=${3:-''}
  IS_DOCKER=${4:-0}

  if [ -z "$WP_SITE_ABSOLUTE_PATH" ]; then
    echo -e "${YELLOW}MISSING ARGUMENTS: plugin|theme and wp-site-absolute-path are required.${NC}"
    echo -e "${YELLOW}Usage function cslant_net_wp_suite_symlink <plugin|theme> <wp-site-absolute-path> <wp-object(plugin/theme)-name>${NC}"
    exit 1
  fi

  if [ "$TYPE" = 'plugin' ]; then
    SYMLINK_PATH="$WP_SITE_ABSOLUTE_PATH/wp-content/plugins/$WP_OBJECT_NAME"
  elif [ "$TYPE" = 'theme' ]; then
    SYMLINK_PATH="$WP_SITE_ABSOLUTE_PATH/wp-content/themes/$WP_OBJECT_NAME"
  fi

  # Check if the source directory exists to remove the symlink
  if [ -d $SYMLINK_PATH ]; then
    echo -e "${YELLOW}Removing existing symlink or directory: $SYMLINK_PATH${NC}"
    rm -rf "$SYMLINK_PATH"
  fi

  if [ "$TYPE" = 'plugin' ]; then
    if [ "$IS_DOCKER" -eq "1" ]; then
      echo -e "${BLUE}🐳 Docker 📂 Symlinking CSlant.net WP Plugins $WP_OBJECT_NAME with the slug: $WP_SITE_ABSOLUTE_PATH...${NC}"
      cd "$MAIN_CURRENT_DIR" || exit
      docker compose run --rm -w /var/dev nginx ash -l -c "\
        ln -sfn /var/dev/wp-cslant-suite /var/dev/wordpress/$WP_SITE_ABSOLUTE_PATH/wp-content/plugins/$WP_OBJECT_NAME
      "
      echo -e "${BLUE}🐳 Docker 📂 Activating CSlant.net WP Plugin $WP_OBJECT_NAME...${NC}"
      docker compose run --rm -w /var/dev php84 ash -l -c "\
        wp plugin activate $WP_OBJECT_NAME --path=/var/dev/wordpress/$WP_SITE_ABSOLUTE_PATH
      "
    else
      check_directory_is_exists "$WP_SITE_ABSOLUTE_PATH"
      echo -e "\n${BLUE}📂 Symlinking CSlant.net WP $TYPE to $WP_SITE_ABSOLUTE_PATH...${NC}"
      ln -s "$SOURCE_DIR/wp-cslant-suite" "$WP_SITE_ABSOLUTE_PATH/wp-content/plugins/$WP_OBJECT_NAME"

        # check if wp cli is installed
        if command -v wp &> /dev/null; then
          wp plugin activate "$WP_OBJECT_NAME" --path="$WP_SITE_ABSOLUTE_PATH" || {
            echo -e "${RED}Failed to activate plugin: $WP_OBJECT_NAME${NC}"
            exit 1
          }
        fi
    fi

  elif [ "$TYPE" = 'theme' ]; then
    TARGET_OBJECT_NAME="$WP_OBJECT_NAME"
    if [ "$WP_OBJECT_NAME" = 'all' ]; then
      TARGET_OBJECT_NAME='*'
    fi

    if [ "$IS_DOCKER" -eq "1" ]; then
      echo -e "${BLUE}🐳 Docker 📂 Symlinking CSlant.net WP Themes $WP_OBJECT_NAME with the slug: $WP_SITE_ABSOLUTE_PATH...${NC}"
      cd "$MAIN_CURRENT_DIR" || exit
      docker compose run --rm -w /var/dev nginx ash -l -c "\
        ln -sfn /var/dev/wp-cslant-themes/$TARGET_OBJECT_NAME /var/dev/wordpress/$WP_SITE_ABSOLUTE_PATH/wp-content/themes/
      "
    else
      check_directory_is_exists "$WP_SITE_ABSOLUTE_PATH"
      echo -e "\n${BLUE}📂 Symlinking CSlant.net WP $TYPE to $WP_SITE_ABSOLUTE_PATH...${NC}"
      ln -s "$SOURCE_DIR/wp-cslant-themes/$TARGET_OBJECT_NAME" "$WP_SITE_ABSOLUTE_PATH/wp-content/themes/"
    fi
  else
    echo -e "${RED}Invalid type: $TYPE. Use 'plugin' or 'theme'.${NC}"
    exit 1
  fi

  if [ "$IS_DOCKER" -eq "1" ]; then
    echo -e "${GREEN}🐳 Docker 📂 Symlinked CSlant.net WP $TYPE: $WP_OBJECT_NAME to $WP_SITE_ABSOLUTE_PATH successfully!${NC}"
  else
    echo -e "${GREEN}📂 Symlinked CSlant.net WP $TYPE: $WP_OBJECT_NAME to $WP_SITE_ABSOLUTE_PATH successfully!${NC}"
  fi
}
