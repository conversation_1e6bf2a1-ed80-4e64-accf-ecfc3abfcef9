#!/bin/bash

install_wp_cli() {
# Check if WP-CLI is already installed
    if command -v wp > /dev/null 2>&1; then
        echo -e "\n${GREEN}✅  WP-CLI is already installed.${NC}"
        return 0
    fi

    echo -e "${YELLOW}📦 Installing WP-CLI...${NC}"

    # Check for sudo permission
    if ! sudo -v > /dev/null 2>&1; then
        echo -e "${RED}Sudo is required to install WP-CLI. Please run this script with sudo.${NC}"
        return 1
    fi

    # Check if PHP is installed
    if ! command -v php > /dev/null 2>&1; then
        echo -e "${RED}PHP is required to run WP-CLI. Please install PHP first.${NC}"
        return 1
    fi

    # Check if curl is installed
    if ! command -v curl > /dev/null 2>&1; then
        echo -e "${RED}Curl is required to download WP-CLI. Please install curl first.${NC}"
        return 1
    fi

    # Step 1: Download WP-CLI phar file
    curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error downloading WP-CLI. Please check your network connection.${NC}"
        return 1
    fi

    # Step 2: Verify the file
    php wp-cli.phar --info
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error verifying WP-CLI file. Please check PHP.${NC}"
        return 1
    fi

    # Step 3: Make executable & move to /usr/local/bin
    chmod +x wp-cli.phar
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error making WP-CLI file executable. Please check permissions.${NC}"
        return 1
    fi

    sudo mv wp-cli.phar /usr/local/bin/wp
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error moving WP-CLI to /usr/local/bin. Please check permissions.${NC}"
        return 1
    fi

    # Step 4: Final check
    echo -e "${GREEN}✅ WP-CLI installation complete!${NC}"
    wp --info
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error verifying WP-CLI installation. Please check the setup.${NC}"
        return 1
    fi

    echo -e "${GREEN}WP-CLI has been installed successfully!${NC}"
    return 0
}

docker_install_wp_cli() {
    echo "Do you want to install WP-CLI in Docker? (y/n)"
    read -r INSTALL_IN_DOCKER
    if [[ "$INSTALL_IN_DOCKER" != "y" && "$INSTALL_IN_DOCKER" != "Y" ]]; then
        echo -e "${YELLOW}Skipping WP-CLI installation in Docker.${NC}"
        return 0
    fi

    # Check if WP-CLI is already installed in Docker
    if docker compose run --rm php84 wp --info > /dev/null 2>&1; then
        echo -e "${GREEN}✅ WP-CLI is already installed in Docker.${NC}"
        return 0
    fi

    echo -e "${YELLOW}📦 Installing WP-CLI in Docker...${NC}"

    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Docker is not running. Please start Docker first.${NC}"
        return 1
    fi

    docker_start_service_php_source

    # Use /tmp for download, then move to /usr/local/bin as root
    docker compose run --rm --user root -w /tmp php84 ash -l -c "\
        curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar && \\
        chmod +x wp-cli.phar && \\
        php wp-cli.phar --info && \\
        mv wp-cli.phar /usr/local/bin/wp && \\
        wp --info\
    "
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error installing WP-CLI in Docker. Please check the setup.${NC}"
        return 1
    fi

    echo -e "${GREEN}✅ WP-CLI has been installed in Docker successfully!${NC}"
}
