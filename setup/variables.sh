#!/bin/bash

GITHUB_SSH_URL=${GITHUB_SSH_URL:-**************:cslant}
GITHUB_COMMUNITY_SSH_URL=${GITHUB_COMMUNITY_SSH_URL:-**************:cslant-community}

GITLAB_SSH_URL=${GITLAB_SSH_URL:-******************}

# shellcheck disable=SC2034
GITHUB_TOKEN=${GITHUB_TOKEN:-ghp_1234567890}
GITLAB_TOKEN=${GITLAB_TOKEN:-1234567890}

# shellcheck disable=SC2034
MAIN_CURRENT_DIR=$(pwd)

# shellcheck disable=SC2034
SOURCE_DIR=$(readlink -f "$SOURCE_CODE_PATH")

# Color variables
YELLOW='\033[1;33m'
RED='\033[1;31m'
CYAN='\033[1;36m'
GREEN='\033[1;32m'
BLUE='\033[1;34m'
RED_0='\033[0;31m'
GREEN_0='\033[0;32m'
BLUE_0='\033[0;34m'
CYAN_0='\033[0;36m'
NC='\033[0m' # No Color

# shellcheck disable=SC2034
BLOG_PACKAGE_REPO_NAMES=(
  'blog-api-package'
  'blog-core'
)

DOMAINS=(
  "${CSLANT_DOMAIN}"
  "v1-${CSLANT_DOMAIN}"
  "${API_DOMAIN}"
  "${CSMS_DOMAIN}"
  "${BLOG_API_DOMAIN}"
  "${BLOG_ADMIN_DOMAIN}"
  "${LARAVEL_BLOG_DOMAIN}"
  "${REPORTS_DOMAIN}"
  "${DOCS_DOMAIN}"
  "${API_DOCS_DOMAIN}"
  "${ASSETS_DOMAIN}"
  "${CSLANT_NET_DOMAIN:-cslant.net}"
)

# shellcheck disable=SC2034
BLOG_DATABASE_FILE_NAME=${BLOG_DATABASE_FILE_NAME:-cslant_blog}

GIT_SYNC_CONFIG_DIR="$MAIN_CURRENT_DIR/git-info"
GIT_SYNC_CONFIG_REPO_FILE="$GIT_SYNC_CONFIG_DIR/git/repos.json"
GIT_SYNC_CONFIG_USERNAMES_FILE="$GIT_SYNC_CONFIG_DIR/git/usernames.json"
