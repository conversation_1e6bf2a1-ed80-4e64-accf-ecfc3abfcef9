#!/bin/bash

resource() {
  resource_lang
  resource_public
  resource_database
  home_resource "$1"
#   blog_resource "$1"

  echo '✨ Syncing resources done!'
  echo ''
}

resource_database() {
  resource_psql_database
}

resource_psql_database() {
  echo ''
  echo '» 🚀 Downloading database file... 🚀'

  DATABASE_PATH="$MAIN_CURRENT_DIR/postgres/entry.d"

  if [ -f "$DATABASE_PATH/$BLOG_DATABASE_FILE_NAME.sql" ]; then
    echo "» Database file already exists"
    echo ''
    return
  fi

  curl \
    -H "Authorization: token $GITHUB_TOKEN" \
    -L https://raw.githubusercontent.com/cslant-community/docker-dev-storage/docker/database/postgres/cslant_blog.sql \
    -o "$DATABASE_PATH/$BLOG_DATABASE_FILE_NAME.sql"
}

home_resource() {
  REPO_NAME='home-resource'

  cd "$SOURCE_DIR" || exit

  git_force_sync "$1" "$REPO_NAME"
  git_handler "$REPO_NAME" '' "$GITHUB_COMMUNITY_SSH_URL"
  echo ''
}

blog_resource() {
  REPO_NAME='blog-resource'

  cd "$SOURCE_DIR" || exit

  git_force_sync "$1" "$REPO_NAME"
  git_handler "$REPO_NAME" '' "$GITHUB_COMMUNITY_SSH_URL"
  echo ''
}

resource_lang() {
  LANG_PATH="$SOURCE_CODE_PATH/blog-admin/lang"

  if [ ! -d "$LANG_PATH" ]; then
    mkdir -p "$LANG_PATH"
  fi

  curl \
    -H "Authorization: token $GITHUB_TOKEN" \
    -L https://raw.githubusercontent.com/cslant-community/blog-storage/docker/lang/Archive.zip \
    -o "$LANG_PATH/Archive.zip"

  unzip -o "$LANG_PATH/Archive.zip" -d "$LANG_PATH"
}

resource_public() {
  PUBLIC_PATH="$SOURCE_CODE_PATH/blog-admin/public"

  if [ ! -d "$PUBLIC_PATH" ]; then
    mkdir -p "$PUBLIC_PATH"
  fi

  curl \
    -H "Authorization: token $GITHUB_TOKEN" \
    -L https://raw.githubusercontent.com/cslant-community/blog-storage/docker/public/Archive.zip \
    -o "$PUBLIC_PATH/Archive.zip"

  unzip -o "$PUBLIC_PATH/Archive.zip" -d "$PUBLIC_PATH"
}
