#!/bin/bash

# ======================== CSlant.net Git Sync ========================

# Flant (Laravel) sync functions
cslant_net_flant_sync() {
  git_sync_handle 'cslant.net-flant' 'cslant.net' "$1"
}

# WordPress sync functions
cslant_net_wp_base_sync() {
  git_sync_handle 'cslant.net-wp-base' '' "$1"
}

cslant_net_wp_core_sync() {
  git_sync_handle 'cslant.net-wp-core' '' "$1"
}

cslant_net_wp_suite_sync() {
  git_sync_handle 'cslant.net_wp_cslant-suite' 'wp-cslant-suite' "$1"
}

cslant_net_wp_themes_sync() {
  git_sync_handle 'cslant.net_wp_cslant-themes' 'wp-cslant-themes' "$1"
}

cslant_net_sync() {
  echo '📥 Syncing CSlant.net repositories...'

  FORCE=0
  if [ "$2" = '-f' ] || [ "$2" = '--force' ]; then
    FORCE=1
  fi

  case "$1" in
    flant)
      cslant_net_flant_sync "$FORCE"
      ;;
    wp-base)
      cslant_net_wp_base_sync "$FORCE"
      ;;
    wp-core)
      cslant_net_wp_core_sync "$FORCE"
      ;;
    wp-suite)
      cslant_net_wp_suite_sync "$FORCE"
      ;;
    wp-themes)
      cslant_net_wp_themes_sync "$FORCE"
      ;;
    all)
      cslant_net_flant_sync "$FORCE"
      cslant_net_wp_base_sync "$FORCE"
      cslant_net_wp_suite_sync "$FORCE"
      cslant_net_wp_themes_sync "$FORCE"
      ;;
    *)
      usage
      echo ''
      echo "Invalid argument: $1"
      exit 1
      ;;
  esac

  echo -e "${GREEN} ✨  Syncing CSlant.net repositories done!${NC}"
  echo ''
}
