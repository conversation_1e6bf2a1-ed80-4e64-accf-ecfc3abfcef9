#!/bin/bash

git_handler() {
  REPO_NAME=$1
  REPO_DIR=${2:-$REPO_NAME}
  REPO_SSH_URL=${3:-none}

  [ -z "$REPO_DIR" ] && REPO_DIR=$REPO_NAME

  if [ -z "$(ls -A "$REPO_DIR" 2>/dev/null)" ]; then
    CLONE_REPO_SSH_URL="$REPO_SSH_URL/$REPO_NAME.git"

    case "$REPO_SSH_URL" in
      "$GITHUB_SSH_URL")
        echo "      ∟ Cloning $REPO_NAME repository from GitHub..."
        CLONE_REPO_SSH_URL="$GITHUB_SSH_URL/$REPO_NAME.git"
        ;;
      "$GITLAB_SSH_URL")
        echo "      ∟ Cloning $REPO_NAME repository from GitLab..."
        GITLAB_PATH=$(get_gitlab_repo_path "$REPO_NAME" "$GIT_SYNC_CONFIG_REPO_FILE")
        if [ -n "$GITLAB_PATH" ]; then
          echo "      ∟ Found GitLab path from config: $GITLAB_PATH"
          CLONE_REPO_SSH_URL="$GITLAB_SSH_URL/$GITLAB_PATH.git"
        else
          echo "      ∟ No GitLab path found in config. Using default GitLab path."
          CLONE_REPO_SSH_URL="$GITLAB_SSH_URL/$REPO_NAME.git"
        fi
        ;;
      none)
        echo "      ∟ Auto-detecting GitLab path from config (default fallback)..."
        GITLAB_PATH=$(get_gitlab_repo_path "$REPO_NAME" "$GIT_SYNC_CONFIG_REPO_FILE")
        if [ -n "$GITLAB_PATH" ]; then
          CLONE_REPO_SSH_URL="$GITLAB_SSH_URL/$GITLAB_PATH.git"
          echo "      ∟ Found GitLab path: $GITLAB_PATH → Cloning from GitLab: $CLONE_REPO_SSH_URL"
        else
          echo "      ∟ No GitLab path found → Cloning from GitHub"
          CLONE_REPO_SSH_URL="$GITHUB_SSH_URL/$REPO_NAME.git"
        fi
        ;;
      *)
        echo "      ∟ Unknown SSH source, fallback to GitHub: $CLONE_REPO_SSH_URL"
        CLONE_REPO_SSH_URL="$GITHUB_SSH_URL/$REPO_NAME.git"
        ;;
    esac

    echo -e "${CYAN}      ∟ Final get clone URL: ${YELLOW}$CLONE_REPO_SSH_URL${NC}"
    git clone "$CLONE_REPO_SSH_URL" "$REPO_DIR"

  else
    echo -e "${CYAN}  ∟ Pulling ${YELLOW}$REPO_NAME${CYAN} repository...${NC}"
    cd "$REPO_DIR" || exit

    # Skip if .git directory doesn't exist and not forcing
    if [ ! -d ".git" ] && [ "$FORCE" != 1 ]; then
      echo -e "${YELLOW}⚠️  Skipping $REPO_DIR - not a git repository${NC}"
      return
    fi

    # Ensure we're on main and up-to-date
    if git rev-parse --verify origin/main >/dev/null 2>&1; then
      echo -e "${CYAN}◎ Switching to main branch and updating from origin...${NC}"
      git fetch origin
      git switch -C main origin/main
    else
      echo -e "${CYAN}◎ Switching to main branch...${NC}"
      git switch main 2>/dev/null || git checkout main -f
    fi

    echo -e "${CYAN}◎ Pulling latest changes...${NC}"
    git pull
  fi
}

git_force_sync() {
  FORCE=${1:-0}
  REPO_DIR=$2

  # Skip if .git directory doesn't exist and not forcing
  if [ ! -d "$REPO_DIR/.git" ] && [ "$FORCE" != 1 ]; then
    echo -e "${YELLOW}⚠️  Skipping $REPO_DIR - not a git repository${NC}"
    return
  fi

  if [ "$FORCE" = 1 ]; then
    echo -e "${RED}» Force syncing ${YELLOW}$REPO_DIR${RED} repository...${NC}"
    rm -rf "$REPO_DIR"
  else
    echo -e "${CYAN}📥 » Syncing ${YELLOW}$REPO_DIR${CYAN} repository...${NC}"
  fi
}

git_sync_handle() {
  REPO_NAME=$1
  REPO_DIR=${2:-}

  [ -z "$REPO_DIR" ] && REPO_DIR=$REPO_NAME

  cd "$SOURCE_DIR" || exit

  git_force_sync "$3" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}
