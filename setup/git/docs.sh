#!/bin/bash

# ======================== Docs Git Sync ========================
docs_sync_handle() {
  docs_sync "$1"
  laravel_like_docs_sync "$1"
  telegram_git_notifier_docs_sync "$1"
  laravel_telegram_git_notifier_sync "$1"
  github_project_php_sync "$1"
}

docs_sync() {
  git_sync_handle 'docs' '' "$1"
}

laravel_like_docs_sync() {
  REPO_NAME='laravel-like-docs'

  echo '📥 Syncing Laravel like docs...'

  cd "$SOURCE_DIR/docs/repos" || exit

  git_force_sync "$1" "$REPO_NAME"
  git_handler "$REPO_NAME"
  echo ''
}

telegram_git_notifier_docs_sync() {
  REPO_NAME='telegram-git-notifier-docs'

  echo '📥 Syncing Laravel Telegram Git Notifier docs...'

  cd "$SOURCE_DIR/docs/repos" || exit

  git_force_sync "$1" "$REPO_NAME"
  git_handler "$REPO_NAME"
  echo ''
}

laravel_telegram_git_notifier_sync() {
  REPO_NAME='laravel-telegram-git-notifier'
  REPO_DIR='telegram-git-notifier'

  echo '📥 Syncing Telegram Git Notifier...'

  cd "$SOURCE_DIR/docs/repos" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}

github_project_php_sync() {
  REPO_NAME='github-project-php-docs'

  echo '📥 Syncing Github Project PHP docs...'

  cd "$SOURCE_DIR/docs/repos" || exit

  git_force_sync "$1" "$REPO_NAME"
  git_handler "$REPO_NAME"
  echo ''
}
