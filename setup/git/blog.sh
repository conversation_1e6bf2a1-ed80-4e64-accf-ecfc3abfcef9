#!/bin/bash

# ======================== Blog Git Sync ========================
blog_admin_sync() {
  git_sync_handle 'blog-admin' '' "$1"
}

blog_fe_sync() {
  git_sync_handle 'blog-fe' '' "$1"
}

blog_private_modules_sync() {
  REPO_NAME='blog-private-modules'

  echo '📥 Syncing private modules...'

  cd "$SOURCE_DIR/blog-admin" || exit

  git_force_sync "$1" "$REPO_NAME"
  git_handler "$REPO_NAME"
  echo ''
}

blog_package_sync() {
  REPO_NAME=$1

  cd "$SOURCE_DIR/blog-admin" || exit

  if [ ! -d "packages" ]; then
    mkdir packages
  fi

  cd packages || exit

  git_force_sync "$2" "$REPO_NAME"
  git_handler "$REPO_NAME"
  echo ''
}

blog_all_packages_sync() {
  for package in "${BLOG_PACKAGE_REPO_NAMES[@]}"; do
    blog_package_sync "$package" "$1"
  done
}

blog_api_package_sync() {
  echo '📥 Syncing api package...'

  blog_package_sync "${BLOG_PACKAGE_REPO_NAMES[0]}" "$1"
}

blog_core_package_sync() {
  echo '📥 Syncing core package...'

  blog_package_sync "${BLOG_PACKAGE_REPO_NAMES[1]}" "$1"
}

blog_git_sync() {
  echo '📥 Syncing blog repositories...'

  FORCE=0
  if [ "$2" = '-f' ] || [ "$2" = '--force' ]; then
    FORCE=1
  fi

  case "$1" in
    admin)
      blog_admin_sync "$FORCE"
      ;;

    fe)
      blog_fe_sync "$FORCE"
#       mfe_header_sync "$FORCE"
#       mfe_footer_sync "$FORCE"
#       mfe_shared_sync "$FORCE"
#       mfe_contact_sync "$FORCE"
      ;;

    api-package)
      blog_api_package_sync "$FORCE"
      ;;

    core-package)
      blog_core_package_sync "$FORCE"
      ;;

    all-packages)
      blog_all_packages_sync "$FORCE"
      ;;

    private-modules)
      blog_private_modules_sync "$FORCE"
      ;;

    all)
      blog_admin_sync "$FORCE"
      blog_private_modules_sync "$FORCE"
#       blog_fe_sync "$FORCE"
#       mfe_header_sync "$FORCE"
#       mfe_footer_sync "$FORCE"
#       mfe_shared_sync "$FORCE"
#       mfe_contact_sync "$FORCE"
      blog_all_packages_sync "$FORCE"
      ;;

    *)
      usage
      exit 1
      ;;
  esac

  echo '✨ Syncing blog repositories done!'
  echo ''
}
# ======================== Blog Git Sync ========================
