#!/bin/bash

# ======================== Home Git Sync ========================
home_api_sync() {
  git_sync_handle 'home-api' '' "$1"
}

home_fe_sync() {
  git_sync_handle 'home-fe' '' "$1"
}

home_fe2_sync() {
  git_sync_handle 'home-fe2' '' "$1"
}

home_git_sync() {
  echo '📥 Syncing Home repositories...'

  FORCE=0
  if [ "$2" = '-f' ] || [ "$2" = '--force' ]; then
    FORCE=1
  fi

  case "$1" in
    api)
      home_api_sync "$FORCE"
      ;;

    fe)
      home_fe_sync "$FORCE"
      home_fe2_sync "$FORCE"
#       mfe_header_sync "$FORCE"
#       mfe_footer_sync "$FORCE"
#       mfe_shared_sync "$FORCE"
#       mfe_contact_sync "$FORCE"
      ;;

    all)
      home_api_sync "$FORCE"
      home_fe_sync "$FORCE"
      home_fe2_sync "$FORCE"
#       mfe_header_sync "$FORCE"
#       mfe_footer_sync "$FORCE"
#       mfe_shared_sync "$FORCE"
#       mfe_contact_sync "$FORCE"
      ;;

    *)
      usage
      exit 1
      ;;
  esac

  echo '✨ Syncing home repositories done!'
  echo ''
}
# ======================== Home Git Sync ========================
