#!/bin/bash

# ========================= Handlers for Repo Installations ========================
special_install() {
  case "$1" in # $2: -f or --force
    "reports")
      reports_sync $2
      reports_source_implement "install"
      docker_start_service_php_source
      ;;
    "docs")
      docs_sync_handle $2
      docs_source_implement "install"
      ;;
    "api-docs")
      api_docs_sync $2
      api_docs_source_implement "install"
      ;;
    "home")
      home_git_sync all "$2"
      home_api_source_implement "install"
      #home_source_implement "$1"
      home_v2_source_implement

      docker_start_services "home-fe2 nginx php84 mysql worker84"
      ;;
    "blog")
      blog_git_sync all "$2"
      home_v2_source_implement
      blog_source_implement "install"
      docker_start_service_php_source
      ;;
    *)
      echo "Invalid argument: $1"
      ;;
  esac
}

# ========================= Client Installations ========================
client_install() {
  case "$1" in
    "vws")
      source $MAIN_CURRENT_DIR/setup/clients/vietnamwebsubmit.sh
      vietnam_web_submit_runner "$2" "$3"
      ;;
    "comic-news")
      source $MAIN_CURRENT_DIR/setup/clients/comic-news.sh
      comic_news_runner "$2" "$3"
      ;;
    *)
      echo "Invalid argument: $1"
      ;;
  esac
}

# =============================================================================
# ========================= Main Source Implementation ========================
# =============================================================================

main_source_implement() {
  home_api_source_implement "$1"
  #home_source_implement "$1"
  home_v2_source_implement
  blog_source_implement "$1"
}

install() {
  echo '🚀 Installing all repo with Docker 🚀'
  echo ''
  cd "$MAIN_CURRENT_DIR" || exit
  echo "◎ Installing all repo with Docker..."

  main_source_implement "install"
}

update() {
  echo '🚀 Updating blog with Docker 🚀'
  echo ''
  cd "$MAIN_CURRENT_DIR" || exit
  echo "◎ Updating blog with Docker..."

  main_source_implement "update"
}

scout_import() {
  echo '🚀 Importing Elasticsearch data 🚀'

  blog_scout_import
}

#### ========================= Home ========================
home_source_implement() {
  echo -e "${GREEN}  ∟ Home Fe ...${NC}"
  env "$SOURCE_DIR/home-fe"
  docker compose run --rm -w /var/dev/home-fe node22 ash -l -c "\
    yarn install; \
  "

  if [ "$HOME_FE_COMMAND" == "start" ]; then
    echo -e "${CYAN}  ∟ Building home-fe...${NC}"
    docker compose run --rm -w /var/dev/home-fe node22 ash -l -c "\
      yarn build; \
    "
  fi
}

home_v2_source_implement() {
  echo -e "${GREEN}  ∟ Home Fe V2 ...${NC}"
  env "$SOURCE_DIR/home-fe2"
  docker compose run --rm -w /var/dev/home-fe2 node22 ash -l -c "\
    yarn install; \
  "

  if [ "$HOME_FE2_COMMAND" == "start" ]; then
    echo -e "${CYAN}  ∟ Building home-fe2...${NC}"
    docker compose run --rm -w /var/dev/home-fe2 node22 ash -l -c "\
      yarn build; \
    "
  fi
}

home_api_source_implement() {
  if [ "$1" == "install" ]; then
    COMPOSER_COMMAND="install"
  else
    COMPOSER_COMMAND="update"
  fi

  echo -e "${GREEN}  ∟ Home API Package...${NC}"
  env "$SOURCE_DIR/home-api"
  docker compose run --rm -w /var/dev/home-api php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "
}

#### ======================== Blog ========================
blog_source_implement() {
  if [ "$1" == "install" ]; then
    COMPOSER_COMMAND="install"
  else
    COMPOSER_COMMAND="update"
  fi

  echo -e "${GREEN}  ∟ Blog Core Package...${NC}"
  docker compose run --rm -w /var/dev/blog-admin/packages/"${BLOG_PACKAGE_REPO_NAMES[1]}" php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  echo "  ∟ Blog API Package..."
  docker compose run --rm -w /var/dev/blog-admin/packages/"${BLOG_PACKAGE_REPO_NAMES[0]}" php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  echo "  ∟ Blog Admin..."
  env "$SOURCE_DIR/blog-admin"
  docker compose run --rm -w /var/dev/blog-admin php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
    php artisan l5-swagger:generate; \
  "
}

blog_fe_source_implement() {
  echo "  ∟ Blog Fe..."
  docker compose run --rm -w /var/dev/blog-fe node22 ash -l -c "\
    yarn install; \
  "

  if [ "$BLOG_FE_COMMAND" == "start" ]; then
    echo "  ∟ Building blog-fe..."
    env "$SOURCE_DIR/blog-fe"
    docker compose run --rm -w /var/dev/blog-fe node22 ash -l -c "\
      yarn build; \
    "
  fi
}

blog_scout_import() {
  docker compose run --rm -w /var/dev/blog-admin php84 ash -l -c "\
    php artisan scout:import \
      \"CSlant\Blog\Core\Models\User\" \
  "
}

#### ========================= Reports ========================
reports_source_implement() {
  if [ "$1" == "install" ]; then
    COMPOSER_COMMAND="install"
  else
    COMPOSER_COMMAND="update"
  fi

  echo "  ∟ Reports..."
  env "$SOURCE_DIR/reports"
  docker compose run --rm -w /var/dev/reports php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
  "

  reload_nginx_service
}

#### ========================= Docs ========================
docs_source_implement() {
  echo "  ∟ Docs..."
  env "$SOURCE_DIR/docs"
  cd "$MAIN_CURRENT_DIR" || exit
  docker compose run --rm -w /var/dev/docs node22 ash -l -c "\
    yarn install; \
  "

  if [ "$DOCS_COMMAND" == "serve" ]; then
    echo -e "${GREEN}  ∟ Building docs...${NC}"
    env "$SOURCE_DIR/docs"
    docker compose run --rm -w /var/dev/docs node22 ash -l -c "\
      yarn build; \
    "
  fi

  cd "$MAIN_CURRENT_DIR" || exit
  echo -e "${CYAN}◎ Starting docs container...${NC}"
  docker compose up -d docs

  reload_nginx_service
}

#### ========================= API Docs ========================
api_docs_source_implement() {
  echo -e "${GREEN}  ∟ API Docs...${NC}"
  env "$SOURCE_DIR/api-docs"
  docker compose run --rm -w /var/dev/api-docs node22 ash -l -c "\
    yarn install; \
  "

#   # Handle to generate the api-docs
#   docker compose run --rm -w /var/dev/api-docs node22 ash -l -c "\
#     yarn docusaurus clean-api-docs all; \
#     yarn docusaurus gen-api-docs all; \
#   "

  if [ "$DOCS_COMMAND" == "serve" ]; then
    echo "  ∟ Building docs..."
    env "$SOURCE_DIR/api-docs"
    docker compose run --rm -w /var/dev/api-docs node22 ash -l -c "\
      yarn build; \
    "
  fi

  cd "$MAIN_CURRENT_DIR" || exit
  docker compose up -d api-docs

  reload_nginx_service
}


# ==================================================================================
# ======================== CSlant.net Source Implementation ========================
# ==================================================================================

cslant_net_handler() {
    case "$1" in
      install)
        case "$2" in
          "wp-cli")
            echo -e "${CYAN}→ Installing WP-CLI...${NC}"
            install_wp_cli
            # docker_install_wp_cli
            ;;

          *)
            echo -e "${RED}Usage: $0 cslant.net install [wp-cli]${NC}"
            exit 1
            ;;
        esac
        ;;
      express-web | ew)
        shift
        express_web "$@"
        ;;
      "flant")
        echo -e "${CYAN}→ Syncing flant...${NC}"
        cslant_net_sync "flant" "${2:-none}"
        cslant_net_flant_source_implement "install"
        docker_start_service_php_source
        ;;
      "wp-base")
        echo -e "${CYAN}→ Syncing wp-base...${NC}"
        cslant_net_sync "wp-base" "${2:-none}"
        docker_start_service_php_source
        ;;
      "wp-core")
        echo -e "${CYAN}→ Syncing wp-core...${NC}"
        cslant_net_sync "wp-core" "${2:-none}"
        ;;
      "wp-suite")
        echo -e "${CYAN}→ Syncing wp-suite...${NC}"
        cslant_net_sync "wp-suite" "${2:-none}"
        ;;
      "wp-themes")
        echo -e "${CYAN}→ Syncing wp-themes...${NC}"
        cslant_net_sync "wp-themes" "${2:-none}"
        ;;
      "wp-suite-symlink")
        if [ -z "$3" ] || [ -z "$4" ]; then
          echo -e "${YELLOW}Usage: $0 cslant.net wp-suite-symlink <plugin|theme> <source> <target>${NC}"
          exit 1
        fi
        echo -e "${BLUE}→ Symlinking wp-suite plugin: $3 to $4${NC}"
        cslant_net_wp_symlink 'plugin' "$3" "$4" "${5:-0}"
        ;;
      "wp-themes-symlink")
        if [ -z "$3" ] || [ -z "$4" ]; then
          echo -e "${YELLOW}Usage: $0 cslant.net wp-themes-symlink <plugin|theme> <source> <target>${NC}"
          exit 1
        fi
        echo -e "${BLUE}→ Symlinking wp-themes theme: $3 to $4${NC}"
        cslant_net_wp_symlink 'theme' "$3" "$4" "${5:-0}"
        ;;
      *)
        echo -e "${RED}Usage: $0 cslant.net [flant|wp]${NC}"
        exit 1
        ;;
    esac
}

cslant_net_flant_source_implement() {
  if [ "$1" = "install" ]; then
    COMPOSER_COMMAND="install"
  else
    COMPOSER_COMMAND="update"
  fi

  echo -e "${GREEN}  ∟ Flant (Laravel) Setup...${NC}"
  env "$SOURCE_DIR/cslant.net"

  # Install PHP dependencies
  docker compose run --rm -w /var/dev/cslant.net php84 ash -l -c "\
    composer $COMPOSER_COMMAND; \
    cp -n .env.example .env || true; \
  "

  # Install NPM dependencies and build assets
  docker compose run --rm -w /var/dev/cslant.net node22 ash -l -c "\
    npm install; \
    npm run build; \
  "
}
