#!/bin/bash

env() {
  REPO_DIR=$1

  cd "$REPO_DIR" || exit

  # sync env file
  if [ ! -f .env ]; then
    echo -e "${CYAN_0}"
    echo -e "${YELLOW}🔧 Setting up environment variables 🔧${NC}"

    echo -e "${BLUE_0}◎ Setting up environment variables...${NC}"

#     if ! command -v envsubst &> /dev/null; then
#       cp .env.example .env
#     else
      envsubst <"$REPO_DIR/.env.example" >"$REPO_DIR/.env"
#     fi
  fi

  cd "$MAIN_CURRENT_DIR" || exit
}

force_sync_accept() {
  echo -e "${CYAN_0}"
  echo -e "${RED_0}⚠️⚠️⚠️ Warning: This will remove all local changes and checkout to main branch! 🆘${NC}"
  echo -e "${YELLOW}Do you want to continue? (y/n)${NC}"
  read -r CONTINUE

  if [ "$CONTINUE" = 'y' ]; then
    echo -e "${BLUE_0}» Processing ...${NC}"
  else
    echo -e "${RED_0}🚫 Syncing cancelled${NC}"
    exit 1
  fi
}

docker_start_services() {
    SERVICE_NAME=$1

    # Check if the service name is multiple (Ex: SERVICE_NAME="nginx mysql php84" -> SERVICE_NAMES=("nginx" "mysql" "php84"))
    if [[ "$SERVICE_NAME" == *" "* ]]; then
        IFS=' ' read -r -a SERVICE_NAMES <<< "$SERVICE_NAME"
    else
        SERVICE_NAMES=("$SERVICE_NAME")
    fi

    for SERVICE in "${SERVICE_NAMES[@]}"; do
        docker_start_single_service "$SERVICE"
    done
    echo -e "\n${GREEN}🐳 ✔ All services started successfully!${NC}"
}

docker_start_single_service() {
  SERVICE_NAME=$1
  echo -e "${CYAN_0}"
  echo -e "${YELLOW}🔄 Starting/Reloading Docker service: $SERVICE_NAME...${NC}"
  cd "$MAIN_CURRENT_DIR" || exit
  if [ ! "$(docker ps -q -f name="$SERVICE_NAME")" ]; then
    docker compose down "$SERVICE_NAME"
  fi
  docker compose up -d "$SERVICE_NAME"
}

reload_nginx_service() {
  docker_start_single_service nginx
}

docker_start_service_php_source() {
  cd "$MAIN_CURRENT_DIR" || exit
  docker_start_services "php84 mysql nginx worker84"
}

define_default_repos() {
  # Ensure SOURCE_DIR exists
  if [ ! -d "$SOURCE_DIR" ]; then
    echo -e "${YELLOW}Creating source directory: $SOURCE_DIR${NC}"
    mkdir -p "$SOURCE_DIR"
  fi

  cd "$SOURCE_DIR" || {
    echo -e "${RED_0}Error: Cannot change to directory $SOURCE_DIR${NC}"
    echo -e "${YELLOW}Creating directory and trying again...${NC}"
    mkdir -p "$SOURCE_DIR"
    cd "$SOURCE_DIR" || exit 1
  }

  mkdir -p docs
  mkdir -p api-docs
  mkdir -p home-fe
  mkdir -p home-fe2
  mkdir -p blog-fe
  mkdir -p mustang-backend
  mkdir -p vietnamwebsubmit/vws-networking-tool
}

check_directory_is_exists() {
  if [ -d "$1" ]; then
    echo -e "${GREEN}Directory $1 exists.${NC}"
    return 0
  else
    echo -e "${RED}Directory $1 does not exist.${NC}"
    return 1
  fi
}
