#!/bin/bash

welcome() {
  echo -e "${GREEN}"
  echo '
██████╗  ██████╗  ██████╗███████╗██╗      █████╗ ███╗   ██╗████████╗
██╔══██╗██╔═══██╗██╔════╝██╔════╝██║     ██╔══██╗████╗  ██║╚══██╔══╝
██║  ██║██║   ██║██║     ███████╗██║     ███████║██╔██╗ ██║   ██║
██║  ██║██║   ██║██║     ╚════██║██║     ██╔══██║██║╚██╗██║   ██║
██████╔╝╚██████╔╝╚██████╗███████║███████╗██║  ██║██║ ╚████║   ██║
╚═════╝  ╚═════╝  ╚═════╝╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝
'
  echo '⚡ Welcome to the CSlant DevOps setup script!'
  echo "- Current dir        : $MAIN_CURRENT_DIR"
  echo "- Source dir         : $SOURCE_DIR"
  echo -e "${NC}"
}

usage() {
  welcome

  echo -e "${YELLOW}Usage:${NC} bash $0 [command] [args] [options]"
  echo ''
  echo -e "${CYAN}Commands:${NC}"
  printf "${GREEN}%-22s %-8s %-50s${NC}\n" "Command" "Alias" "Description"
  printf "${YELLOW}%-22s %-8s %-50s${NC}\n" "---------------------" "------" "--------------------------------------------------"
  printf "%-22s %-8s %-50s\n" "welcome" "w" "Show welcome message"
  printf "%-22s %-8s %-50s\n" "help" "h" "Show this help message"
  printf "%-22s %-8s %-50s\n" "build" "b" "Build base src with Docker"
  printf "%-22s %-8s %-50s\n" "build_all" "ba" "Build src with Docker (All services)"
  printf "%-22s %-8s %-50s\n" "network" "n" "Create Docker network"
  printf "%-22s %-8s %-50s\n" "blog_git_sync" "bgs" "Sync blog repositories"
  printf "%-22s %-8s %-50s\n" "home_git_sync" "hgs" "Sync home repositories"
  printf "%-22s %-8s %-50s\n" "special_install" "si" "Install special repository"
  printf "%-22s %-8s %-50s\n" "start" "s" "Start the necessary docker containers"
  printf "%-22s %-8s %-50s\n" "start_all" "sa" "Start all docker containers (All services)"
  printf "%-22s %-8s %-50s\n" "install" "i" "Install all src dependencies"
  printf "%-22s %-8s %-50s\n" "update" "u" "Update all src dependencies"
  printf "%-22s %-8s %-50s\n" "resource" "r" "Download src resources"
  printf "%-22s %-8s %-50s\n" "es_import" "ei" "Import Elasticsearch data"
  printf "%-22s %-8s %-50s\n" "all" "a" "Sync git repositories and build blog, home [In house]"
  printf "%-22s %-8s %-50s\n" "ssl" "---" "Generate SSL certificates for local development"
  printf "%-22s %-8s %-50s\n" "docker_sync_repo" "dsr" "Sync the configures for Docker hub"
  echo ''
  echo '------------------------------------------------------'
  echo ''
  echo -e "${CYAN}Args for 'blog_git_sync | bgs':${NC}"
  printf "${GREEN}%-18s %-50s${NC}\n" "Arg" "Description"
  printf "${YELLOW}%-18s %-50s${NC}\n" "-----------------" "---------------------------------------------"
  printf "%-18s %-50s\n" "admin" "Sync blog-admin repository"
  printf "%-18s %-50s\n" "fe" "Sync blog-fe repository"
  printf "%-18s %-50s\n" "api-package" "Sync blog-api-package repository"
  printf "%-18s %-50s\n" "core-package" "Sync blog-core repository"
  printf "%-18s %-50s\n" "all-packages" "Sync all blog packages repositories"
  printf "%-18s %-50s\n" "private-modules" "Sync blog-private-modules repository"
  printf "%-18s %-50s\n" "all" "Sync all related blog repositories"
  echo ''
  echo '  ∟ Options:'
  echo '    -f, --force   Force sync repositories (Remove existing directories and clone again)'
  echo ''
  echo '------------------------------------------------------'
  echo -e "${CYAN}Args for 'special_install | si':${NC}"
  printf "${GREEN}%-18s %-50s${NC}\n" "Arg" "Description"
  printf "${YELLOW}%-18s %-50s${NC}\n" "-----------------" "---------------------------------------------"
  printf "%-18s %-50s\n" "reports" "Install reports repository"
  printf "%-18s %-50s\n" "docs" "Install docs and all submodules repositories"
  printf "%-18s %-50s\n" "api-docs" "Install api-docs repository"
  echo ''
  echo -e "${CYAN}Examples:${NC}"
  PURPLE='\033[1;35m'
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh help"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh build"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh blog_git_sync admin"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh blog_git_sync all"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh blog_git_sync all -f"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh install"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh start"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh start_all"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh all"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh si reports"
  printf "${PURPLE}%-40s${NC}\n" "bash ./runner.sh ssl"
  echo ''
  echo '------------------------------------------------------'
  echo ''
  echo -e "${YELLOW}Makefile${NC} ${CYAN}Commands:${NC}"
  printf "${GREEN}%-50s %-50s %-80s${NC}\n" "Command" "Real Command" "Description"
  printf "${YELLOW}%-50s %-50s %-80s${NC}\n" "-------------------------------------------------" "------------------------------" "--------------------------------------------------"
  printf "%-50s %-50s %-80s\n" "help" "./runner.sh help" "Show runner.sh help"
  printf "%-50s %-50s %-80s\n" "dc-up-b" "docker compose up -d --build" "Start all containers and rebuild if needed"
  printf "%-50s %-50s %-80s\n" "dc-up" "docker compose up -d" "Start all containers"
  printf "%-50s %-50s %-80s\n" "dc-down" "docker compose down" "Stop and remove all containers"
  printf "%-50s %-50s %-80s\n" "dc-logs" "docker compose logs -f" "Show logs for all containers"

  printf "%-50s %-50s %-80s\n" "inhouse-api-docs" "./runner.sh si api-docs" "Install api-docs repository"
  printf "%-50s %-50s %-80s\n" "inhouse-docs" "./runner.sh si docs" "Install docs and submodules"
  printf "%-50s %-50s %-80s\n" "inhouse-home" "./runner.sh si home" "Install home repository"
  echo ''
  printf "%-50s %-50s %-80s\n" "cslant.net repo=?" 'Sync the option repo from cslant.net' "Sync cslant.net repository"
  printf "%-50s %-50s %-80s\n" "cslant.net-flant" "./runner.sh cslant.net flant" "Install cslant.net site"
  printf "%-50s %-50s %-80s\n" "cslant.net-wp-suite-map path=?" 'Check makefile to see command' "Sync cslant.net wp-suite and symlink"
  printf "%-50s %-50s %-80s\n" "cslant.net-wp-themes-map name=? path=?" 'Check makefile to see command' "Sync cslant.net wp-themes and symlink"
  echo ''
  printf "%-50s %-50s %-80s\n" "start-ent-vws" "./runner.sh ent vws start" "Start ent-vws service"
  printf "%-50s %-50s %-80s\n" "start-ci-comic-news" "./runner.sh ci comic-news start" "Start comic-news service"
  echo ''
}
