#!/bin/bash

docker_sync_repo() {
  echo -e "${CYAN_0}"
  echo -e "${YELLOW}🐳 Syncing Docker config 🌐${NC}"
  cd "$MAIN_CURRENT_DIR" || exit

  echo -e "${BLUE_0}◎ Syncing Docker config...${NC}"

  DC_CONFIG_DIR="docker-images"

  if [ -d "$MAIN_CURRENT_DIR"/"$DC_CONFIG_DIR" ]; then
    echo -e "${GREEN}  ∟ Docker config already exists${NC}"
    cd "$DC_CONFIG_DIR" || exit
    echo -e "${YELLOW}  ∟ Updating Docker config...${NC}"
    git pull origin main
  else
    echo -e "${YELLOW}  ∟ Cloning Docker config...${NC}"
    git clone "$GITHUB_SSH_URL/dev-docker-images.git" "$DC_CONFIG_DIR"
  fi
}
