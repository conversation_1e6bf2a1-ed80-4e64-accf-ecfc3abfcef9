#!/bin/bash

cd "$MAIN_CURRENT_DIR" || exit

source ./setup/configs/git-sync.sh

if [[ "$1" == "help" || "$1" == "h" || "$1" == "usage" || "$1" == "w" ||
      "$1" == "welcome" || "$1" == "ssl" || "$1" == "network" ||
      "$1" == "build" || "$1" == "b" || "$1" == "build_all" ||
      "$1" == "ba" || "$1" == "install" || "$1" == "i" ]]; then
  echo -e "${YELLOW}Skipping git sync for help or usage or some other commands.${NC}"
else
  git_sync_repo
fi

cd "$MAIN_CURRENT_DIR" || exit
source ./git-info/scripts/git-info.sh

source ./setup/configs/docker.sh
source ./setup/helpers/tips.sh
source ./setup/git/global.sh
source ./setup/git/blog.sh
source ./setup/git/docs.sh
source ./setup/git/home.sh
source ./setup/git/others.sh
source ./setup/git/module-federation.sh
source ./setup/configs/ssl.sh
source ./setup/helpers/helpers.sh
source ./setup/git/resource.sh

source ./setup/git/cslant.net.sh
source ./setup/wordpress/install-wpcli.sh
if [ -f "./setup/wordpress/express-web.sh" ]; then
  source ./setup/wordpress/express-web.sh
fi

source ./setup/functions.sh
source ./setup/handlers.sh
