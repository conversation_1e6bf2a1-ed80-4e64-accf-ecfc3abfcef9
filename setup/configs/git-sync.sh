#!/bin/bash

git_sync_repo() {
  echo -e "${CYAN_0}"
  echo -e "${YELLOW}📡 Syncing git config 🌐${NC}"

  cd "$MAIN_CURRENT_DIR" || exit
  echo -e "${BLUE_0}◎ Syncing git config...${NC}"

  GIT_CONFIG_DIR="git-info"

  if [ -d "$MAIN_CURRENT_DIR"/"$GIT_CONFIG_DIR/scripts" ]; then
    echo -e "${GREEN}  ∟ git config already exists${NC}"
    cd "$GIT_CONFIG_DIR" || exit
    echo -e "${YELLOW}  ∟ Updating git config...${NC}"
    git pull origin main
  else
    echo -e "${YELLOW}  ∟ Cloning git config...${NC}"
    git clone "$GITHUB_SSH_URL/git-sync-config.git" "$GIT_CONFIG_DIR"
  fi
}
