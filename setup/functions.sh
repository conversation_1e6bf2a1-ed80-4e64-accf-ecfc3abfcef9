#!/bin/bash

build() {
  build_handler
  docker compose build
  docker compose pull nginx mysql php84 php83 worker84 node22 mailhog
}

build_all() {
  build_handler
  docker compose -f docker-compose.yml -f docker-compose-tools.yml build
  docker compose -f docker-compose.yml -f docker-compose-tools.yml pull nginx mysql php84 php83 worker84 worker83 node22 mailhog elasticsearch
}

start() {
  echo -e "${GREEN}🚀 Start the necessary docker containers 🚀${NC}"
  echo ''
  cd "$MAIN_CURRENT_DIR" || exit

  docker_start_services "nginx php84 mysql node22 home-fe2 mailhog worker84 elasticsearch"
}

start_all() {
  echo -e "${GREEN}🚀 Start all docker containers (All services) 🚀${NC}"
  echo ''
  cd "$MAIN_CURRENT_DIR" || exit
  echo -e "${CYAN}◎ Starting all docker containers with all services and tools...${NC}"
  docker compose -f docker-compose.yml -f docker-compose-tools.yml up -d
}

elasticsearch_import() {
  echo -e "${GREEN}🚀 Importing Elasticsearch data 🚀${NC}"
  echo ''
  cd "$MAIN_CURRENT_DIR" || exit
  echo -e "${CYAN}◎ Importing Elasticsearch data...${NC}"

  scout_import
}

network() {
  echo ''
  echo -e "${GREEN}🐳 Creating \"cslant_dev\" docker network 🌐${NC}"
  cd "$MAIN_CURRENT_DIR" || exit

  DEV_NETWORK=cslant_dev

  echo -e "${CYAN}◎ Checking for existing network...${NC}"
  if [ -z "$(docker network ls -q -f name=$DEV_NETWORK)" ]; then
    echo -e "${YELLOW}◎ Network not found. Creating new network...${NC}"
    docker network create $DEV_NETWORK
    echo -e "${GREEN}✔ Network 'cslant_dev' created.${NC}"
  else
    echo -e "${YELLOW}∟ Network 'cslant_dev' already exists.${NC}"
  fi
}

build_handler() {
  echo -e "${GREEN}🐳 Building src with Docker 🐳${NC}"

  network

  echo ''
  cd "$MAIN_CURRENT_DIR" || exit
  echo -e "${CYAN}◎ Build blog with Docker...${NC}"
}
