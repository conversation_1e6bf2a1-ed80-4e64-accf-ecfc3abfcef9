#    location / {
#        try_files $uri $uri/ /index.html?$query_string /index.php?$query_string;
#
#        location ~* /wp-content/.*\.php$ {
#            deny all;
#        }
#
#        location ~* /wp-include/.*\.php$ {
#            deny all;
#        }
#    }

# add upload body
client_max_body_size 4000M;

## ============ WORDPRESS ================
location / {
    try_files $uri $uri/ /index.html?$query_string /index.php?$query_string;
    
    # Security headers for WordPress
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}

location ~ \.php$ {
    try_files $uri /index.php =404;
    fastcgi_pass $upstream_backend84;
    fastcgi_index index.php;
    fastcgi_buffers 16 16k;
    fastcgi_buffer_size 32k;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_read_timeout 600;
    include fastcgi_params;
}

## ============ MEETUP ================
location /meetup {
    root $upstream_source_path/vws-meetup/public;
    try_files $uri $uri/ @php-meetup;
}

location /meetup/ {
    alias $upstream_source_path/vws-meetup/public/;
    try_files $uri $uri/ @php-meetup;
}

location @php-meetup {
    fastcgi_pass $upstream_backend74;
    include fastcgi_params;

    fastcgi_param SCRIPT_FILENAME $upstream_source_path/vws-meetup/public/index.php;

    add_header 'Access-Control-Allow-Origin' '$http_origin' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'content-type,x-xsrf-token' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Max-Age' 1728000 always;
}

##
# Others
##
location = /registration {
    rewrite ^/registration$ https://vietnamwebsummit.com/meetup/detail/vietnam-web-summit-2025-446 permanent;
}

## ============ MEETUP STATIC ================
location /static {
    alias $upstream_source_path/vws-static/public;
    try_files $uri $uri/ @php-static;
    
    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header X-Content-Type-Options "nosniff" always;
    }
}

location @php-static {
    fastcgi_pass $upstream_backend74;
    include fastcgi_params;

    fastcgi_param SCRIPT_FILENAME $upstream_source_path/vws-static/index.php;

    add_header 'Access-Control-Allow-Origin' '$http_origin' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'content-type,x-xsrf-token' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Max-Age' 1728000 always;
}

## ============ MEETUP CMS ================
location /meetup-cms {
    root $upstream_source_path/vws-meetup-cms;
    try_files $uri $uri/ @php-meetup-cms;
}

location @php-meetup-cms {
    fastcgi_pass $upstream_backend56;
    include fastcgi_params;

    fastcgi_param SCRIPT_FILENAME $upstream_source_path/vws-meetup-cms/index.php;

    add_header 'Access-Control-Allow-Origin' '$http_origin' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'content-type,x-xsrf-token' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Max-Age' 1728000 always;
}

## ============ NETWORKING ================
location ~* ^/(networking|clockwork|sanctum|__clockwork|__debugbar|_debugbar|_ignition) {
    root $upstream_source_path/vws-networking-tool/public;
    try_files $uri $uri/ @php-networking;
    
    # Security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}

location @php-networking {
    fastcgi_pass $upstream_backend84;
    include fastcgi_params;

    fastcgi_param SCRIPT_FILENAME $upstream_source_path/vws-networking-tool/public/index.php;

    add_header 'Access-Control-Allow-Origin' '$http_origin' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'content-type,x-xsrf-token' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Max-Age' 1728000 always;
    
    # Security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}

# Block access to hidden files
location ~ /\.ht {
    deny all;
}

location ~ /\.(?!well-known).* {
    deny all;
}

## ============ PDF FILES ================
location ~* \.pdf$ {
    # Serve PDF files from this folder
    root $upstream_source_path/vws-static/vietnamwebsummit-pdf;

    # Try to find the file
    try_files /$uri =404;

    # Set proper MIME type (optional, usually auto-detected)
    types {
        application/pdf pdf;
    }

    # Caching
    expires 30d;
    add_header Cache-Control "public, max-age=2592000";

    # CORS headers
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, HEAD, OPTIONS' always;

    # Security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Disable directory listing
    autoindex off;
    
    # Limit methods to only what's needed
    limit_except GET HEAD OPTIONS {
        deny all;
    }
}

# Block access to backup and source files
location ~* \.(bak|bk|swp|old|orig|original|php#|php~|php_bak|save|swo|swp|sql)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Block PHP execution in uploads directory
location ~* /(?:uploads|files)/.*\.php$ {
    deny all;
}

# # PDF file
# location ~* ^/(.*)\.pdf {
#     root /var/dev/topdev-files;
#     log_not_found on;
#     access_log off;
# }
