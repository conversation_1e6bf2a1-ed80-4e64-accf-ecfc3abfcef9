server {
    listen 80;
    listen [::]:80;

    server_name outsrc.tien.news.cslant.com.local;
    root /var/dev/cl-tien-comic-news-platform/frontend/public;
    index index.php index.html index.htm;

    access_log /var/log/clients/outsrc.tien.news.cslant.com.local-access.log;
    error_log /var/log/clients/outsrc.tien.news.cslant.com.local-error.log debug;

    set $upstream_frontend comic-news:3000;

    location / {
        proxy_pass http://$upstream_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        # proxy_read_timeout 86400s;
        # proxy_send_timeout 86400s;
    }

    location ~* /_next/ {
      alias /var/dev/cl-tien-comic-news-platform/frontend/.next;

      try_files $uri $uri/ @fe;
    }

    location @fe {
      proxy_pass http://$upstream_frontend;

      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
    }

    location /sheepi/ {
        proxy_pass http://$upstream_frontend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /static/ {
        root /path/to/your/nextjs/out/;
    }

    location ~* ^/(admin|cn-api|api|_debugbar|ajax|robots.txt|feed) {
        root /var/dev/cl-tien-comic-news-platform/backend/public;
        try_files $uri $uri/ @php-blog;
    }

    location ~* \.(xml|ico|json)$ {
        root /var/dev/cl-tien-comic-news-platform/backend/public;
        try_files $uri $uri/ @php-blog;
    }

    location @php-blog {
        fastcgi_pass php84:9000;
        include fastcgi_params;

        fastcgi_param SCRIPT_FILENAME /var/dev/cl-tien-comic-news-platform/backend/public/index.php;

        add_header 'Access-Control-Allow-Origin' '$http_origin' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'content-type,x-xsrf-token' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Max-Age' 1728000 always;
    }

    location ^~ /vendor {
        allow all;
        alias /var/dev/cl-tien-comic-news-platform/backend/public/vendor;
    }

    location /_debugbar {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ^~ /storage {
        allow all;
        alias /var/dev/cl-tien-comic-news-platform/backend/public/storage;
    }

    location ^~ /themes {
        allow all;
        alias /var/dev/cl-tien-comic-news-platform/backend/public/themes;
    }

    location ~ /\.ht {
        deny all;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
