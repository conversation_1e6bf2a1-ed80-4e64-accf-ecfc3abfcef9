
# Run as a unique, less privileged user for security reasons
user www-data;

# Use 1 worker process for development
worker_processes 1;

# Error logs to stderr for docker logs
error_log /dev/stderr debug;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    multi_accept on;
    use epoll;
}

http {
    # Show nginx version for debugging
    server_tokens on;
    
    # MIME types
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Charset
    charset UTF-8;
    
    # Logging - detailed for development
    log_format dev '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /dev/stdout dev;
    error_log /dev/stderr debug;
    
    # Performance - optimized for development
    sendfile on;
    tcp_nopush off;
    tcp_nodelay on;
    
    # Timeouts - longer for debugging
    keepalive_timeout 65s;
    client_header_timeout 60s;
    client_body_timeout 60s;
    send_timeout 60s;
    
    # Buffer sizes - smaller for development
    client_body_buffer_size 16k;
    client_max_body_size 128M;
    large_client_header_buffers 4 8k;
    
    # Disable file cache for development
    open_file_cache off;
    
    # Enable gzip for faster development
    gzip on;
    gzip_comp_level 3;
    gzip_min_length 1000;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # SSL Configuration - allow all for development
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers off;
    
    # Security headers - minimal for development
    add_header X-Content-Type-Options "nosniff";
    
    # FastCGI settings - for PHP
    fastcgi_read_timeout 300s;
    fastcgi_buffers 8 16k;
    fastcgi_buffer_size 32k;
    
    # Proxy settings - for Node.js and other backends
    proxy_read_timeout 300s;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    
    # Disable proxy buffering for real-time response
    proxy_buffering off;
    
    # Include additional configs
    include /etc/nginx/conf.d/*;
    
    # DNS resolver with Docker's internal DNS
    resolver 127.0.0.11 valid=30s;
}
