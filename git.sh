#!/bin/bash

####################### VietnamWebSummit #######################
vws_networking_tool_sync() {
  REPO_NAME='ent-vnwebsubmit-networking-tool'
  REPO_DIR='vws-networking-tool'

  echo '📥 Syncing VietnamWebSummit Networking Tool...'

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}

vws_meetup_sync() {
  REPO_NAME='ent-vnwebsubmit-meetup'
  REPO_DIR='vws-meetup'

  echo '📥 Syncing VietnamWebSummit Meetup...'

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}

vws_meetup_static_sync() {
  REPO_NAME='ent-vnwebsubmit-meetup-static'
  REPO_DIR='vws-static'

  echo '📥 Syncing VietnamWebSummit Meetup Static...'

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}

vws_meetup_cms_sync() {
  REPO_NAME='ent-vnwebsubmit-meetup-cms'
  REPO_DIR='vws-meetup-cms'

  echo '📥 Syncing VietnamWebSummit Meetup CMS...'

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}

vws_landing_page_sync() {
  REPO_NAME='ent-vnwebsubmit-wp-LP'
  REPO_DIR='vws-homepage'

  echo '📥 Syncing VietnamWebSummit Landing Page...'

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"
  echo ''
}

vws_storages_sync() {
  REPO_NAME='ent-vnwebsubmit-storage'
  REPO_DIR='vws-storage'
  REPO_DIR2='vws-storage2'
  REPO_DIR3='vws-storage3'

  echo '📥 Syncing VietnamWebSummit Storages...'

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit

  git_force_sync "$1" "$REPO_DIR"
  git_handler "$REPO_NAME" "$REPO_DIR"

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit
  git_force_sync "$1" "$REPO_DIR"2
  git_handler "$REPO_NAME"2 "$REPO_DIR2"
  echo ''

  cd "$SOURCE_DIR/vietnamwebsubmit" || exit
  git_force_sync "$1" "$REPO_DIR3"
  git_handler 'ent-vnwebsubmit-storage3' "$REPO_DIR3"
  echo ''
}

vietnam_web_submit_git_sync() {
  echo '📥 Syncing VietnamWebSummit repositories...'

  force_sync_accept

  FORCE=0
  if [ "$2" = '-f' ] || [ "$2" = '--force' ]; then
    FORCE=1
  fi

  case "$1" in
    networking-tool)
      vws_networking_tool_sync "$FORCE"
      ;;
    meetup)
      vws_meetup_sync "$FORCE"
      ;;
    meetup-static)
      vws_meetup_static_sync "$FORCE"
      ;;
    meetup-cms)
      vws_meetup_cms_sync "$FORCE"
      ;;
    landing-page)
      vws_landing_page_sync "$FORCE"
      ;;
    storages)
      vws_storages_sync "$FORCE"
      ;;
    all)
      vws_networking_tool_sync "$FORCE"
      vws_meetup_sync "$FORCE"
      vws_meetup_static_sync "$FORCE"
      vws_meetup_cms_sync "$FORCE"
      vws_landing_page_sync "$FORCE"
      vws_storages_sync "$FORCE"
      ;;
    *)
      usage
      exit 1
      ;;
  esac

  echo '✨ Syncing VietnamWebSummit repositories done!'
  echo ''
}
#---------------------- VietnamWebSummit -----------------------
